import fs from 'fs';
import path from 'path';
import { SiteSettings } from '../types/admin';
import { defaultSiteSettings } from '../data/settings';

// مسار ملف الإعدادات
const SETTINGS_FILE_PATH = path.join(process.cwd(), 'src', 'data', 'settings.json');

// قراءة الإعدادات من الملف
export function readSettingsFromFile(): SiteSettings {
  try {
    // التحقق من وجود الملف
    if (!fs.existsSync(SETTINGS_FILE_PATH)) {
      console.log('⚠️ ملف الإعدادات غير موجود، سيتم إنشاؤه بالإعدادات الافتراضية');
      writeSettingsToFile(defaultSiteSettings);
      return defaultSiteSettings;
    }

    // قراءة الملف
    const fileContent = fs.readFileSync(SETTINGS_FILE_PATH, 'utf8');
    const settings = JSON.parse(fileContent);

    // دمج مع الإعدادات الافتراضية للتأكد من وجود جميع الحقول
    const mergedSettings = {
      ...defaultSiteSettings,
      ...settings,
      // التأكد من وجود الكائنات المتداخلة
      socialLinks: {
        ...defaultSiteSettings.socialLinks,
        ...settings.socialLinks
      },
      headerSettings: {
        ...defaultSiteSettings.headerSettings,
        ...settings.headerSettings
      },
      footerSettings: {
        ...defaultSiteSettings.footerSettings,
        ...settings.footerSettings,
        companyInfo: {
          ...defaultSiteSettings.footerSettings.companyInfo,
          ...settings.footerSettings?.companyInfo
        }
      },
      contactSettings: {
        ...defaultSiteSettings.contactSettings,
        ...settings.contactSettings,
        mapSettings: {
          ...defaultSiteSettings.contactSettings.mapSettings,
          ...settings.contactSettings?.mapSettings
        },
        contactFormSettings: {
          ...defaultSiteSettings.contactSettings.contactFormSettings,
          ...settings.contactSettings?.contactFormSettings
        },
        officeHours: {
          ...defaultSiteSettings.contactSettings.officeHours,
          ...settings.contactSettings?.officeHours
        },
        additionalInfo: {
          ...defaultSiteSettings.contactSettings.additionalInfo,
          ...settings.contactSettings?.additionalInfo
        }
      }
    };

    console.log('✅ تم تحميل الإعدادات من الملف بنجاح');
    return mergedSettings;

  } catch (error) {
    console.error('❌ خطأ في قراءة ملف الإعدادات:', error);
    console.log('🔄 استخدام الإعدادات الافتراضية');
    return defaultSiteSettings;
  }
}

// كتابة الإعدادات إلى الملف
export function writeSettingsToFile(settings: SiteSettings): boolean {
  try {
    // التأكد من وجود المجلد
    const settingsDir = path.dirname(SETTINGS_FILE_PATH);
    if (!fs.existsSync(settingsDir)) {
      fs.mkdirSync(settingsDir, { recursive: true });
    }

    // إضافة timestamp للتحديث
    const settingsWithTimestamp = {
      ...settings,
      lastUpdated: new Date().toISOString()
    };

    // كتابة الملف
    fs.writeFileSync(
      SETTINGS_FILE_PATH, 
      JSON.stringify(settingsWithTimestamp, null, 2),
      'utf8'
    );

    console.log('✅ تم حفظ الإعدادات في الملف بنجاح');
    console.log('📁 مسار الملف:', SETTINGS_FILE_PATH);
    return true;

  } catch (error) {
    console.error('❌ خطأ في كتابة ملف الإعدادات:', error);
    return false;
  }
}

// التحقق من إمكانية الكتابة في الملف
export function checkFileWritePermissions(): boolean {
  try {
    const testData = { test: true, timestamp: new Date().toISOString() };
    const testPath = path.join(path.dirname(SETTINGS_FILE_PATH), 'test-write.json');
    
    fs.writeFileSync(testPath, JSON.stringify(testData));
    fs.unlinkSync(testPath);
    
    console.log('✅ صلاحيات الكتابة متوفرة');
    return true;
  } catch (error) {
    console.error('❌ لا توجد صلاحيات كتابة:', error);
    return false;
  }
}

// نسخ احتياطي من الإعدادات
export function backupSettings(): boolean {
  try {
    if (!fs.existsSync(SETTINGS_FILE_PATH)) {
      return false;
    }

    const backupPath = path.join(
      path.dirname(SETTINGS_FILE_PATH),
      `settings-backup-${Date.now()}.json`
    );

    fs.copyFileSync(SETTINGS_FILE_PATH, backupPath);
    console.log('✅ تم إنشاء نسخة احتياطية:', backupPath);
    return true;
  } catch (error) {
    console.error('❌ خطأ في إنشاء النسخة الاحتياطية:', error);
    return false;
  }
}
