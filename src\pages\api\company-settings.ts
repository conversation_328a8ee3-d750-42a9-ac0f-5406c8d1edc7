import { NextApiRequest, NextApiResponse } from 'next';
import fs from 'fs';
import path from 'path';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method === 'POST') {
    try {
      const { email } = req.body;

      // التحقق من صحة البيانات
      if (!email || !email.includes('@')) {
        return res.status(400).json({
          success: false,
          message: 'يرجى إدخال إيميل صحيح'
        });
      }

      // إنشاء مجلد البيانات إذا لم يكن موجوداً
      const dataDir = path.join(process.cwd(), 'src', 'data');
      if (!fs.existsSync(dataDir)) {
        fs.mkdirSync(dataDir, { recursive: true });
      }

      // حفظ إعدادات الشركة
      const companySettings = {
        email,
        updatedAt: new Date().toISOString()
      };

      const settingsPath = path.join(dataDir, 'company-settings.json');
      fs.writeFileSync(settingsPath, JSON.stringify(companySettings, null, 2));

      res.status(200).json({
        success: true,
        message: 'تم حفظ إعدادات الشركة بنجاح',
        settings: companySettings
      });

    } catch (error) {
      console.error('Error saving company settings:', error);
      res.status(500).json({
        success: false,
        message: 'حدث خطأ أثناء حفظ الإعدادات'
      });
    }
  } else if (req.method === 'GET') {
    try {
      const settingsPath = path.join(process.cwd(), 'src', 'data', 'company-settings.json');
      
      if (!fs.existsSync(settingsPath)) {
        return res.status(200).json({
          success: true,
          settings: { email: '' }
        });
      }

      const settingsContent = fs.readFileSync(settingsPath, 'utf-8');
      const settings = JSON.parse(settingsContent);

      res.status(200).json({
        success: true,
        settings
      });

    } catch (error) {
      console.error('Error loading company settings:', error);
      res.status(500).json({
        success: false,
        message: 'حدث خطأ أثناء تحميل الإعدادات'
      });
    }
  } else {
    res.setHeader('Allow', ['GET', 'POST']);
    res.status(405).end(`Method ${req.method} Not Allowed`);
  }
}
