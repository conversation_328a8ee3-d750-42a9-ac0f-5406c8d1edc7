// اختبار رفع الصور
const fs = require('fs');
const path = require('path');

async function testUploadAPI() {
  console.log('🧪 اختبار API رفع الصور...\n');

  try {
    // إنشاء ملف صورة تجريبي (SVG بسيط)
    const testImageContent = `
      <svg width="100" height="100" xmlns="http://www.w3.org/2000/svg">
        <rect width="100%" height="100%" fill="#4F46E5"/>
        <text x="50%" y="50%" font-family="Arial" font-size="12" fill="white" text-anchor="middle" dominant-baseline="middle">
          TEST
        </text>
      </svg>
    `;

    const testImagePath = path.join(__dirname, 'test-image.svg');
    fs.writeFileSync(testImagePath, testImageContent);

    console.log('✅ تم إنشاء صورة اختبار');

    // اختبار API upload-simple
    const FormData = require('form-data');
    const fetch = require('node-fetch');

    const form = new FormData();
    form.append('files', fs.createReadStream(testImagePath), {
      filename: 'test-image.svg',
      contentType: 'image/svg+xml'
    });

    console.log('📤 إرسال طلب رفع الصورة...');

    const response = await fetch('http://localhost:3000/api/upload-simple', {
      method: 'POST',
      body: form
    });

    console.log(`📊 استجابة الخادم: ${response.status} ${response.statusText}`);

    if (response.ok) {
      const result = await response.json();
      console.log('✅ نجح رفع الصورة!');
      console.log('📋 النتيجة:', JSON.stringify(result, null, 2));
      
      if (result.files && result.files.length > 0) {
        const uploadedFile = result.files[0];
        console.log(`🔗 رابط الصورة: http://localhost:3000${uploadedFile}`);
        
        // اختبار الوصول للصورة المرفوعة
        const imageResponse = await fetch(`http://localhost:3000${uploadedFile}`);
        console.log(`🖼️ اختبار الوصول للصورة: ${imageResponse.ok ? '✅ نجح' : '❌ فشل'} (${imageResponse.status})`);
      }
    } else {
      const error = await response.text();
      console.log('❌ فشل رفع الصورة');
      console.log('📋 الخطأ:', error);
    }

    // تنظيف ملف الاختبار
    fs.unlinkSync(testImagePath);
    console.log('🧹 تم حذف ملف الاختبار');

  } catch (error) {
    console.error('❌ خطأ في اختبار رفع الصور:', error.message);
  }
}

async function testImageEndpoints() {
  console.log('\n🔗 اختبار endpoints الصور...\n');

  const fetch = require('node-fetch');

  // اختبار placeholder API
  try {
    console.log('1. اختبار Placeholder API:');
    const placeholderResponse = await fetch('http://localhost:3000/api/placeholder?width=300&height=200&text=اختبار');
    console.log(`   Status: ${placeholderResponse.status}`);
    console.log(`   Content-Type: ${placeholderResponse.headers.get('content-type')}`);
    console.log(`   نتيجة: ${placeholderResponse.ok ? '✅ يعمل' : '❌ لا يعمل'}`);
  } catch (error) {
    console.log('   ❌ خطأ:', error.message);
  }

  // اختبار uploads API
  try {
    console.log('\n2. اختبار Uploads API:');
    
    // جلب قائمة الملفات الموجودة
    const uploadsDir = path.join(process.cwd(), 'public', 'uploads');
    if (fs.existsSync(uploadsDir)) {
      const files = fs.readdirSync(uploadsDir).filter(f => f.endsWith('.jpg') || f.endsWith('.png') || f.endsWith('.webp'));
      
      if (files.length > 0) {
        const testFile = files[0];
        console.log(`   اختبار ملف: ${testFile}`);
        
        const fileResponse = await fetch(`http://localhost:3000/uploads/${testFile}`);
        console.log(`   Status: ${fileResponse.status}`);
        console.log(`   Content-Type: ${fileResponse.headers.get('content-type')}`);
        console.log(`   نتيجة: ${fileResponse.ok ? '✅ يعمل' : '❌ لا يعمل'}`);
      } else {
        console.log('   ⚠️ لا توجد ملفات صور للاختبار');
      }
    } else {
      console.log('   ❌ مجلد uploads غير موجود');
    }
  } catch (error) {
    console.log('   ❌ خطأ:', error.message);
  }
}

async function checkUploadsDirectory() {
  console.log('\n📁 فحص مجلد uploads...\n');

  const uploadsDir = path.join(process.cwd(), 'public', 'uploads');
  
  try {
    if (fs.existsSync(uploadsDir)) {
      const files = fs.readdirSync(uploadsDir);
      console.log(`📊 عدد الملفات: ${files.length}`);
      
      const imageFiles = files.filter(f => 
        f.endsWith('.jpg') || f.endsWith('.jpeg') || 
        f.endsWith('.png') || f.endsWith('.webp') || f.endsWith('.gif')
      );
      
      console.log(`🖼️ عدد ملفات الصور: ${imageFiles.length}`);
      
      if (imageFiles.length > 0) {
        console.log('📋 أول 3 ملفات صور:');
        imageFiles.slice(0, 3).forEach((file, index) => {
          const filePath = path.join(uploadsDir, file);
          const stats = fs.statSync(filePath);
          console.log(`   ${index + 1}. ${file} (${Math.round(stats.size / 1024)} KB)`);
        });
      }
    } else {
      console.log('❌ مجلد uploads غير موجود');
      console.log('🔧 إنشاء مجلد uploads...');
      fs.mkdirSync(uploadsDir, { recursive: true });
      console.log('✅ تم إنشاء مجلد uploads');
    }
  } catch (error) {
    console.error('❌ خطأ في فحص مجلد uploads:', error.message);
  }
}

async function main() {
  console.log('🚀 بدء اختبار نظام رفع الصور\n');
  
  await checkUploadsDirectory();
  await testImageEndpoints();
  await testUploadAPI();
  
  console.log('\n📋 ملخص الإصلاحات:');
  console.log('1. ✅ تم إنشاء API مبسط لرفع الصور: /api/upload-simple');
  console.log('2. ✅ تم إزالة المصادقة من API رفع الصور');
  console.log('3. ✅ تم تحديث مكون ImageUpload لاستخدام API الجديد');
  console.log('4. ✅ تم إصلاح placeholder الصور لاستخدام API محلي');
  console.log('5. ✅ تم إنشاء SafeImage component لمعالجة أخطاء الصور');
  
  console.log('\n🎯 الخطوات التالية:');
  console.log('1. تأكد من تشغيل الخادم: npm run dev');
  console.log('2. اذهب إلى صفحة إضافة منتج: http://localhost:3000/admin/products');
  console.log('3. جرب رفع صورة جديدة');
  console.log('4. تحقق من ظهور الصورة في قائمة المنتجات');
  
  console.log('\n🏁 انتهاء اختبار نظام رفع الصور');
}

main();
