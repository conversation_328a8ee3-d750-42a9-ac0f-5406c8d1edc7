import React, { useState, useEffect } from 'react';
import Head from 'next/head';
import AdminLayout from '../../components/admin/AdminLayout';

interface QuoteRequest {
  id: string;
  customerInfo: {
    name: string;
    email: string;
    phone: string;
    company: string;
  };
  products: any[];
  totalAmount: number;
  createdAt: string;
  status: 'pending' | 'processed' | 'sent';
  excelFilePath: string;
}

interface CompanySettings {
  email: string;
}

const QuoteRequestsPage = () => {
  const [quoteRequests, setQuoteRequests] = useState<QuoteRequest[]>([]);
  const [loading, setLoading] = useState(true);
  const [showSettings, setShowSettings] = useState(false);
  const [companySettings, setCompanySettings] = useState<CompanySettings>({
    email: ''
  });

  useEffect(() => {
    loadQuoteRequests();
    loadCompanySettings();
  }, []);

  const loadQuoteRequests = async () => {
    try {
      const response = await fetch('/api/quote-requests');
      const result = await response.json();
      
      if (result.success) {
        setQuoteRequests(result.requests);
      }
    } catch (error) {
      console.error('Error loading quote requests:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadCompanySettings = async () => {
    try {
      // محاولة تحميل الإعدادات من الخادم أولاً
      const response = await fetch('/api/company-settings');
      const result = await response.json();

      if (result.success && result.settings.email) {
        setCompanySettings(result.settings);
        // حفظ في localStorage أيضاً للاستخدام المحلي
        localStorage.setItem('companySettings', JSON.stringify(result.settings));
        return;
      }
    } catch (error) {
      console.log('Could not load settings from server, trying localStorage');
    }

    // إذا فشل تحميل الإعدادات من الخادم، استخدم localStorage
    const savedSettings = localStorage.getItem('companySettings');
    if (savedSettings) {
      setCompanySettings(JSON.parse(savedSettings));
    }
  };

  const saveCompanySettings = async () => {
    try {
      // حفظ في localStorage للاستخدام المحلي
      localStorage.setItem('companySettings', JSON.stringify(companySettings));

      // حفظ في الخادم أيضاً
      const response = await fetch('/api/company-settings', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(companySettings),
      });

      if (response.ok) {
        console.log('تم حفظ إعدادات الشركة بنجاح');
      } else {
        console.error('فشل في حفظ إعدادات الشركة');
      }

      setShowSettings(false);
    } catch (error) {
      console.error('خطأ في حفظ إعدادات الشركة:', error);
      // حفظ في localStorage على الأقل
      localStorage.setItem('companySettings', JSON.stringify(companySettings));
      setShowSettings(false);
    }
  };

  const handleDownloadExcel = (excelFilePath: string) => {
    const filename = excelFilePath.split('/').pop();
    window.open(`/api/download-excel/${filename}`, '_blank');
  };

  const handleSendEmail = (request: QuoteRequest) => {
    const subject = `عرض سعر - ${request.id}`;
    const body = `مرحباً ${request.customerInfo.name}،\n\nنشكركم على طلب التسعير. يرجى مراجعة العرض المرفق.\n\nمع تحياتنا،\nفريق دروب هاجر`;
    
    window.open(`mailto:${request.customerInfo.email}?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(body)}`);
  };

  const handleWhatsApp = (request: QuoteRequest) => {
    // قراءة رسالة الواتساب من الإعدادات
    let message = `مرحباً ${request.customerInfo.name}، نشكركم على طلب التسعير رقم ${request.id}. سنتواصل معكم قريباً بالعرض المطلوب.`;

    try {
      const savedSettings = localStorage.getItem('siteSettings');
      if (savedSettings) {
        const settings = JSON.parse(savedSettings);
        if (settings.communicationSettings?.whatsapp?.quoteResponseMessageAr) {
          message = `مرحباً ${request.customerInfo.name}، ${settings.communicationSettings.whatsapp.quoteResponseMessageAr}`;
        }
      }
    } catch (error) {
      console.log('Using default WhatsApp message');
    }

    const phoneNumber = request.customerInfo.phone.replace(/[^0-9]/g, '');
    window.open(`https://wa.me/${phoneNumber}?text=${encodeURIComponent(message)}`);
  };

  const updateRequestStatus = async (requestId: string, status: string) => {
    try {
      const response = await fetch(`/api/quote-requests/${requestId}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ status }),
      });

      if (response.ok) {
        loadQuoteRequests(); // إعادة تحميل البيانات
      }
    } catch (error) {
      console.error('Error updating request status:', error);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('ar-SA');
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'processed': return 'bg-blue-100 text-blue-800';
      case 'sent': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'pending': return 'في الانتظار';
      case 'processed': return 'تم المعالجة';
      case 'sent': return 'تم الإرسال';
      default: return status;
    }
  };

  if (loading) {
    return (
      <>
        <Head>
          <title>طلبات التسعير - VidMeet</title>
        </Head>
        <AdminLayout title="طلبات التسعير">
          <div className="flex items-center justify-center h-64">
            <div className="text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#3B82F6] mx-auto mb-4"></div>
              <p className="text-[#64748B]">جاري التحميل...</p>
            </div>
          </div>
        </AdminLayout>
      </>
    );
  }

  return (
    <>
      <Head>
        <title>طلبات التسعير - VidMeet</title>
        <meta name="description" content="إدارة طلبات التسعير" />
      </Head>

      <AdminLayout title="طلبات التسعير">
        <div className="space-y-6">
          {/* Header */}
          <div className="flex justify-between items-center">
            <div>
              <h1 className="text-2xl font-bold text-[#1E293B] mb-2">
                طلبات التسعير ({quoteRequests.length})
              </h1>
              <p className="text-[#64748B]">إدارة ومتابعة طلبات التسعير من العملاء</p>
            </div>
            
            <button
              onClick={() => setShowSettings(true)}
              className="bg-[#3B82F6] text-white px-4 py-2 rounded-lg hover:bg-[#2563EB] transition-colors flex items-center gap-2"
            >
              <i className="ri-settings-line"></i>
              إعدادات المراسلة
            </button>
          </div>

          {/* Quote Requests Table */}
          <div className="bg-white rounded-2xl shadow-[0_0_50px_rgba(0,0,0,0.05)] border border-[#E2E8F0] overflow-hidden">
            {quoteRequests.length === 0 ? (
              <div className="p-8 text-center">
                <i className="ri-file-list-line text-4xl text-[#64748B] mb-4"></i>
                <p className="text-[#64748B]">لا توجد طلبات تسعير</p>
              </div>
            ) : (
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead className="bg-[#F8FAFC]">
                    <tr>
                      <th className="px-6 py-3 text-right text-xs font-medium text-[#64748B] uppercase tracking-wider">
                        اسم العميل
                      </th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-[#64748B] uppercase tracking-wider">
                        رقم الهاتف
                      </th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-[#64748B] uppercase tracking-wider">
                        البريد الإلكتروني
                      </th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-[#64748B] uppercase tracking-wider">
                        الشركة
                      </th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-[#64748B] uppercase tracking-wider">
                        عدد المنتجات
                      </th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-[#64748B] uppercase tracking-wider">
                        الحالة
                      </th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-[#64748B] uppercase tracking-wider">
                        تاريخ الطلب
                      </th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-[#64748B] uppercase tracking-wider">
                        الإجراءات
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-[#E2E8F0]">
                    {quoteRequests.map((request) => (
                      <tr key={request.id} className="hover:bg-[#F8FAFC]">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="font-medium text-[#1E293B]">
                            {request.customerInfo.name}
                          </div>
                          <div className="text-sm text-[#64748B]">
                            {request.id}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-[#64748B]">
                          {request.customerInfo.phone}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-[#64748B]">
                          {request.customerInfo.email}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-[#64748B]">
                          {request.customerInfo.company || '-'}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-[#64748B]">
                          {request.products.length}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <select
                            value={request.status}
                            onChange={(e) => updateRequestStatus(request.id, e.target.value)}
                            className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full border-0 ${getStatusColor(request.status)}`}
                          >
                            <option value="pending">في الانتظار</option>
                            <option value="processed">تم المعالجة</option>
                            <option value="sent">تم الإرسال</option>
                          </select>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-[#64748B]">
                          {formatDate(request.createdAt)}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex gap-2">
                            <button
                              onClick={() => handleDownloadExcel(request.excelFilePath)}
                              className="bg-[#059669] text-white px-3 py-1 rounded text-sm hover:bg-[#047857] transition-colors"
                              title="فتح Excel"
                            >
                              <i className="ri-file-excel-line"></i>
                            </button>
                            <button
                              onClick={() => handleSendEmail(request)}
                              className="bg-[#3B82F6] text-white px-3 py-1 rounded text-sm hover:bg-[#2563EB] transition-colors"
                              title="إرسال إيميل"
                            >
                              <i className="ri-mail-line"></i>
                            </button>
                            <button
                              onClick={() => handleWhatsApp(request)}
                              className="bg-[#10B981] text-white px-3 py-1 rounded text-sm hover:bg-[#059669] transition-colors"
                              title="واتساب"
                            >
                              <i className="ri-whatsapp-line"></i>
                            </button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </div>
        </div>

        {/* Settings Modal */}
        {showSettings && (
          <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
            <div className="bg-white rounded-2xl p-6 w-full max-w-md">
              <h3 className="text-xl font-bold text-[#1E293B] mb-4">
                إعدادات المراسلة
              </h3>
              
              <div className="space-y-4">
                <div>
                  <label className="block text-[#374151] font-medium mb-2">
                    إيميل استقبال طلبات التسعير
                  </label>
                  <input
                    type="email"
                    value={companySettings.email}
                    onChange={(e) => setCompanySettings(prev => ({ ...prev, email: e.target.value }))}
                    className="w-full px-4 py-2 border border-[#E2E8F0] rounded-lg focus:ring-2 focus:ring-[#3B82F6] focus:border-transparent"
                    placeholder="<EMAIL>"
                  />
                  <p className="text-sm text-[#64748B] mt-1">
                    سيتم إرسال جميع طلبات التسعير مع ملفات Excel إلى هذا الإيميل
                  </p>
                </div>
              </div>
              
              <div className="flex gap-3 mt-6">
                <button
                  onClick={saveCompanySettings}
                  className="flex-1 bg-[#3B82F6] text-white py-2 px-4 rounded-lg font-semibold hover:bg-[#2563EB] transition-colors"
                >
                  حفظ
                </button>
                <button
                  onClick={() => setShowSettings(false)}
                  className="flex-1 bg-[#E2E8F0] text-[#374151] py-2 px-4 rounded-lg font-semibold hover:bg-[#CBD5E1] transition-colors"
                >
                  إلغاء
                </button>
              </div>
            </div>
          </div>
        )}
      </AdminLayout>
    </>
  );
};

export default QuoteRequestsPage;
