import React from 'react';
import { useTranslation } from 'next-i18next';

const StatsSection = () => {
  const { t } = useTranslation('common');

  const stats = [
    {
      value: '+1500',
      label: t('stats.products'),
      icon: 'ri-shopping-bag-3-line',
      color: 'from-blue-400 to-blue-600'
    },
    {
      value: '+200',
      label: t('stats.clients'),
      icon: 'ri-user-smile-line',
      color: 'from-green-400 to-green-600'
    },
    {
      value: '+15',
      label: t('stats.experience'),
      icon: 'ri-award-line',
      color: 'from-yellow-400 to-yellow-600'
    },
    {
      value: '+50',
      label: t('stats.projects'),
      icon: 'ri-building-line',
      color: 'from-purple-400 to-purple-600'
    },
  ];
  return (
    <section className="py-20 bg-gradient-to-br from-primary via-secondary to-primary text-white relative overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute top-10 left-10 w-20 h-20 border border-white/20 rounded-full"></div>
        <div className="absolute top-32 right-20 w-16 h-16 border border-white/20 rounded-full"></div>
        <div className="absolute bottom-20 left-1/4 w-12 h-12 border border-white/20 rounded-full"></div>
        <div className="absolute bottom-10 right-10 w-24 h-24 border border-white/20 rounded-full"></div>
      </div>

      <div className="container mx-auto px-4 relative z-10">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">{t('stats.title')}</h2>
          <p className="text-xl text-gray-200 max-w-2xl mx-auto">
            {t('stats.subtitle')}
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {stats.map((stat, idx) => (
            <div
              key={idx}
              className="group relative bg-white/10 backdrop-blur-sm rounded-2xl p-8 text-center hover:bg-white/20 transition-all duration-300 transform hover:scale-105 hover:shadow-2xl"
            >
              {/* Icon Background */}
              <div className={`w-20 h-20 mx-auto mb-6 rounded-full bg-gradient-to-r ${stat.color} flex items-center justify-center shadow-lg group-hover:shadow-xl transition-all duration-300`}>
                <i className={`${stat.icon} text-3xl text-white`}></i>
              </div>

              {/* Stats Value */}
              <div className="text-4xl md:text-5xl font-bold mb-3 bg-gradient-to-r from-white to-gray-200 bg-clip-text text-transparent">
                {stat.value}
              </div>

              {/* Stats Label */}
              <div className="text-lg md:text-xl font-semibold text-gray-200 group-hover:text-white transition-colors duration-300">
                {stat.label}
              </div>

              {/* Decorative Element */}
              <div className="absolute top-4 right-4 w-8 h-8 border border-white/20 rounded-full group-hover:border-white/40 transition-all duration-300"></div>
            </div>
          ))}
        </div>

        {/* Bottom Decorative Line */}
        <div className="mt-16 flex justify-center">
          <div className="w-24 h-1 bg-gradient-to-r from-transparent via-white to-transparent rounded-full"></div>
        </div>
      </div>
    </section>
  );
};

export default StatsSection;
