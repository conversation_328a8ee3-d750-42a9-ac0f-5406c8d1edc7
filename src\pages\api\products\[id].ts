import { NextApiRequest, NextApiResponse } from 'next';
import { getProductWithDetails } from '../../../lib/mysql-database';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  const { id } = req.query;

  if (typeof id !== 'string') {
    return res.status(400).json({ error: 'Invalid product ID' });
  }

  try {
    switch (req.method) {
      case 'GET':
        const product = await getProductWithDetails(id);
        if (!product) {
          return res.status(404).json({ error: 'Product not found' });
        }
        res.status(200).json(product);
        break;

      // PUT and DELETE methods removed for now
      // Will be implemented later if needed

      default:
        res.setHeader('Allow', ['GET']);
        res.status(405).end(`Method ${req.method} Not Allowed`);
    }
  } catch (error) {
    console.error('API Error:', error);
    res.status(500).json({ error: 'Internal Server Error' });
  }
}
