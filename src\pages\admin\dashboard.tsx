import React, { useState, useEffect } from 'react';
import Head from 'next/head';
import AdminLayout from '../../components/admin/AdminLayout';
import { DashboardStats } from '../../types/admin';

interface QuoteRequest {
  id: string;
  customerInfo: {
    name: string;
    email: string;
    phone: string;
    company: string;
  };
  products: {
    id: string;
    name: string;
    quantity: number;
    price: number;
  }[];
  totalAmount: number;
  createdAt: string;
  status: 'pending' | 'processed' | 'sent';
  excelFilePath: string;
}

const AdminDashboard = () => {
  const [stats, setStats] = useState<DashboardStats>({
    totalProducts: 0,
    totalCategories: 0,
    totalSubcategories: 0,
    activeProducts: 0,
    inactiveProducts: 0,
    featuredProducts: 0
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [successMessage, setSuccessMessage] = useState('');
  const [quoteRequests, setQuoteRequests] = useState<QuoteRequest[]>([]);
  const [showQuoteRequests, setShowQuoteRequests] = useState(false);

  useEffect(() => {
    loadStats();
    loadQuoteRequests();
  }, []);

  const loadStats = async () => {
    try {
      setLoading(true);
      console.log('🔄 جلب إحصائيات لوحة التحكم...');

      // استخدام API الاختبار المبسط أولاً
      const response = await fetch('/api/test-stats');
      const data = await response.json();

      console.log('📊 استجابة API:', data);

      if (data.success && data.stats) {
        const statsData = data.stats;
        console.log('📈 تحديث الإحصائيات:', statsData);

        setStats({
          totalProducts: statsData.products.total,
          totalCategories: statsData.categories.total,
          totalSubcategories: statsData.subcategories.total,
          activeProducts: statsData.products.active,
          inactiveProducts: statsData.products.total - statsData.products.active,
          featuredProducts: statsData.products.featured
        });

        console.log('✅ تم تحديث الإحصائيات بنجاح');
        setSuccessMessage('تم تحديث الإحصائيات بنجاح');
        setError('');

        // إخفاء رسالة النجاح بعد 3 ثوان
        setTimeout(() => setSuccessMessage(''), 3000);
      } else {
        console.error('❌ فشل في جلب الإحصائيات:', data);
        setError(data.messageAr || data.message || 'فشل في جلب الإحصائيات');
        setSuccessMessage('');
      }
    } catch (error) {
      console.error('❌ خطأ في تحميل الإحصائيات:', error);
      setError('خطأ في تحميل الإحصائيات');
    } finally {
      setLoading(false);
    }
  };

  const loadQuoteRequests = async () => {
    try {
      const response = await fetch('/api/quote-requests');
      const result = await response.json();

      if (result.success) {
        setQuoteRequests(result.requests);
      }
    } catch (error) {
      console.error('Error loading quote requests:', error);
    }
  };

  const handleDownloadExcel = (excelFilePath: string) => {
    const filename = excelFilePath.split('/').pop();
    window.open(`/api/download-excel/${filename}`, '_blank');
  };

  const handleSendEmail = (request: QuoteRequest) => {
    const subject = `عرض سعر - ${request.id}`;
    const body = `مرحباً ${request.customerInfo.name}،\n\nنشكركم على طلب التسعير. يرجى مراجعة العرض المرفق.\n\nمع تحياتنا،\nفريق دروب هاجر`;

    window.open(`mailto:${request.customerInfo.email}?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(body)}`);
  };

  const handleWhatsApp = (request: QuoteRequest) => {
    // قراءة رسالة الواتساب من الإعدادات
    let message = `مرحباً ${request.customerInfo.name}، نشكركم على طلب التسعير رقم ${request.id}. سنتواصل معكم قريباً بالعرض المطلوب.`;

    try {
      const savedSettings = localStorage.getItem('siteSettings');
      if (savedSettings) {
        const settings = JSON.parse(savedSettings);
        if (settings.communicationSettings?.whatsapp?.quoteResponseMessageAr) {
          message = `مرحباً ${request.customerInfo.name}، ${settings.communicationSettings.whatsapp.quoteResponseMessageAr}`;
        }
      }
    } catch (error) {
      console.log('Using default WhatsApp message');
    }

    const phoneNumber = request.customerInfo.phone.replace(/[^0-9]/g, '');
    window.open(`https://wa.me/${phoneNumber}?text=${encodeURIComponent(message)}`);
  };


  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('ar-SA');
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'processed': return 'bg-blue-100 text-blue-800';
      case 'sent': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const statsCards = [
    {
      title: 'إجمالي المنتجات',
      value: stats.totalProducts,
      icon: 'ri-product-hunt-line',
      color: 'bg-gradient-to-r from-[#3B82F6] to-[#2563EB]',
      bgColor: 'bg-white',
      textColor: 'text-[#1E293B]',
      trend: { value: 12, isPositive: true }
    },
    {
      title: 'الفئات الرئيسية',
      value: stats.totalCategories,
      icon: 'ri-folder-line',
      color: 'bg-gradient-to-r from-[#10B981] to-[#059669]',
      bgColor: 'bg-white',
      textColor: 'text-[#1E293B]',
      trend: { value: 5, isPositive: true }
    },
    {
      title: 'الفئات الفرعية',
      value: stats.totalSubcategories,
      icon: 'ri-folder-2-line',
      color: 'bg-gradient-to-r from-[#8B5CF6] to-[#7C3AED]',
      bgColor: 'bg-white',
      textColor: 'text-[#1E293B]',
      trend: { value: 8, isPositive: true }
    },
    {
      title: 'المنتجات المتاحة',
      value: stats.activeProducts,
      icon: 'ri-check-line',
      color: 'bg-gradient-to-r from-[#059669] to-[#047857]',
      bgColor: 'bg-white',
      textColor: 'text-[#1E293B]',
      trend: { value: 15, isPositive: true }
    },
    {
      title: 'المنتجات غير المتاحة',
      value: stats.inactiveProducts,
      icon: 'ri-close-line',
      color: 'bg-gradient-to-r from-[#EF4444] to-[#DC2626]',
      bgColor: 'bg-white',
      textColor: 'text-[#1E293B]',
      trend: { value: 3, isPositive: false }
    },
    {
      title: 'المنتجات المميزة',
      value: stats.featuredProducts,
      icon: 'ri-star-line',
      color: 'bg-gradient-to-r from-[#F59E0B] to-[#D97706]',
      bgColor: 'bg-white',
      textColor: 'text-[#1E293B]',
      trend: { value: 20, isPositive: true }
    },
    {
      title: 'طلبات التسعير',
      value: quoteRequests.length,
      icon: 'ri-file-text-line',
      color: 'bg-gradient-to-r from-[#EC4899] to-[#DB2777]',
      bgColor: 'bg-white',
      textColor: 'text-[#1E293B]',
      trend: { value: 25, isPositive: true },
      onClick: () => setShowQuoteRequests(true)
    }
  ];

  const recentActivities = [
    {
      id: 1,
      action: 'تم إضافة منتج جديد',
      item: 'طقم أطباق بورسلين فاخر',
      time: 'منذ ساعتين',
      icon: 'ri-add-line',
      color: 'text-[#059669]',
      bgColor: 'bg-[#ECFDF5]'
    },
    {
      id: 2,
      action: 'تم تحديث فئة',
      item: 'الأطعمة والمشروبات',
      time: 'منذ 4 ساعات',
      icon: 'ri-edit-line',
      color: 'text-[#3B82F6]',
      bgColor: 'bg-[#EFF6FF]'
    },
    {
      id: 3,
      action: 'تم حذف منتج',
      item: 'كاسات زجاجية قديمة',
      time: 'منذ يوم واحد',
      icon: 'ri-delete-bin-line',
      color: 'text-[#EF4444]',
      bgColor: 'bg-[#FEF2F2]'
    },
    {
      id: 4,
      action: 'تم تحديث الإعدادات',
      item: 'معلومات التواصل',
      time: 'منذ يومين',
      icon: 'ri-settings-line',
      color: 'text-[#8B5CF6]',
      bgColor: 'bg-[#F5F3FF]'
    }
  ];

  const quickActions = [
    {
      title: 'إضافة منتج جديد',
      description: 'أضف منتج جديد إلى المتجر',
      icon: 'ri-add-box-line',
      href: '/admin/products',
      color: 'bg-white hover:bg-[#F8FAFC]',
      iconBg: 'bg-[#EFF6FF]',
      iconColor: 'text-[#3B82F6]'
    },
    {
      title: 'إضافة فئة رئيسية',
      description: 'أنشئ فئة رئيسية جديدة',
      icon: 'ri-folder-add-line',
      href: '/admin/categories',
      color: 'bg-white hover:bg-[#F8FAFC]',
      iconBg: 'bg-[#ECFDF5]',
      iconColor: 'text-[#059669]'
    },
    {
      title: 'إعدادات الموقع',
      description: 'تحديث معلومات الموقع',
      icon: 'ri-settings-3-line',
      href: '/admin/settings',
      color: 'bg-white hover:bg-[#F8FAFC]',
      iconBg: 'bg-[#F5F3FF]',
      iconColor: 'text-[#8B5CF6]'
    },
    {
      title: 'عرض الموقع',
      description: 'اذهب إلى الموقع الرئيسي',
      icon: 'ri-external-link-line',
      href: '/',
      color: 'bg-white hover:bg-[#F8FAFC]',
      iconBg: 'bg-[#F1F5F9]',
      iconColor: 'text-[#64748B]',
      external: true
    }
  ];

  return (
    <>
      <Head>
        <title>لوحة التحكم - VidMeet</title>
        <meta name="description" content="لوحة التحكم الرئيسية" />
      </Head>

      <AdminLayout title="لوحة التحكم">
        <div className="space-y-6">


          {/* Success Message */}
          {successMessage && (
            <div className="bg-white border border-[#E2E8F0] text-[#059669] px-6 py-4 rounded-2xl flex items-center shadow-[0_0_50px_rgba(0,0,0,0.05)]">
              <i className="ri-check-line text-xl ml-3"></i>
              <span className="flex-1">{successMessage}</span>
              <button
                onClick={() => setSuccessMessage('')}
                className="text-[#059669] hover:text-[#047857] transition-colors"
              >
                <i className="ri-close-line text-xl"></i>
              </button>
            </div>
          )}

          {/* Error Message */}
          {error && (
            <div className="bg-white border border-[#E2E8F0] text-[#EF4444] px-6 py-4 rounded-2xl flex items-center shadow-[0_0_50px_rgba(0,0,0,0.05)]">
              <i className="ri-error-warning-line text-xl ml-3"></i>
              <span className="flex-1">{error}</span>
              <button
                onClick={() => setError('')}
                className="text-[#EF4444] hover:text-[#DC2626] transition-colors"
              >
                <i className="ri-close-line text-xl"></i>
              </button>
            </div>
          )}

          {/* Welcome Section */}
          <div className="bg-white rounded-2xl p-8 shadow-[0_0_50px_rgba(0,0,0,0.05)] border border-[#E2E8F0]">
            <div className="flex items-center justify-between">
              <div className="flex-1">
                <h1 className="text-2xl font-bold text-[#1E293B] mb-2 flex items-center">
                  <i className="ri-hand-heart-line text-3xl ml-3 text-[#3B82F6]"></i>
                  مرحباً بك في لوحة التحكم
                </h1>
                <p className="text-[#64748B]">إدارة شاملة ومتطورة لموقع VidMeet</p>
                <div className="flex items-center mt-4 text-[#64748B]">
                  <i className="ri-time-line text-lg ml-2"></i>
                  <span>آخر تسجيل دخول: {new Date().toLocaleString('ar-SA')}</span>
                </div>
              </div>

              <div className="flex items-center gap-3">
                <button
                  onClick={loadStats}
                  disabled={loading}
                  className="bg-primary text-white px-4 py-2 rounded-lg hover:bg-primary/90 disabled:opacity-50 transition-colors flex items-center gap-2"
                >
                  {loading ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                      تحديث...
                    </>
                  ) : (
                    <>
                      <i className="ri-refresh-line"></i>
                      تحديث الإحصائيات
                    </>
                  )}
                </button>
              </div>
              <div className="hidden md:block">
                <div className="w-20 h-20 bg-[#F8FAFC] rounded-2xl flex items-center justify-center">
                  <i className="ri-dashboard-3-line text-4xl text-[#3B82F6]"></i>
                </div>
              </div>
            </div>
          </div>

          {/* Stats Cards */}
          {loading ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {[...Array(7)].map((_, index) => (
                <div key={index} className="bg-white rounded-2xl p-6 shadow-[0_0_50px_rgba(0,0,0,0.05)] border border-[#E2E8F0] animate-pulse">
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex-1">
                      <div className="h-4 bg-gray-200 rounded mb-2 w-2/3"></div>
                      <div className="h-8 bg-gray-200 rounded mb-2 w-1/2"></div>
                      <div className="h-3 bg-gray-200 rounded w-1/3"></div>
                    </div>
                    <div className="w-14 h-14 bg-gray-200 rounded-xl"></div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {statsCards.map((card, index) => (
                <div
                  key={index}
                  className={`bg-white rounded-2xl p-6 shadow-[0_0_50px_rgba(0,0,0,0.05)] border border-[#E2E8F0] hover:shadow-[0_0_50px_rgba(0,0,0,0.1)] transition-all duration-300 ${
                    card.onClick ? 'cursor-pointer' : ''
                  }`}
                  onClick={card.onClick}
                >
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex-1">
                      <p className="text-sm font-medium text-[#64748B] mb-2">{card.title}</p>
                      <p className="text-3xl font-bold text-[#1E293B] mb-2">{card.value}</p>
                      {card.trend && (
                        <div className="flex items-center">
                          <i className={`ri-arrow-${card.trend.isPositive ? 'up' : 'down'}-line text-sm ${
                            card.trend.isPositive ? 'text-[#059669]' : 'text-[#EF4444]'
                          } ml-1`}></i>
                          <span className={`text-sm font-medium ${
                            card.trend.isPositive ? 'text-[#059669]' : 'text-[#EF4444]'
                          }`}>
                            {card.trend.value}%
                          </span>
                          <span className="text-sm text-[#64748B] mr-1">هذا الشهر</span>
                        </div>
                      )}
                    </div>
                    <div className={`w-14 h-14 ${card.color} rounded-xl flex items-center justify-center shadow-lg`}>
                      <i className={`${card.icon} text-2xl text-white`}></i>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Recent Activities */}
            <div className="bg-white rounded-2xl p-6 shadow-[0_0_50px_rgba(0,0,0,0.05)] border border-[#E2E8F0]">
              <h2 className="text-lg font-bold text-[#1E293B] mb-4 flex items-center">
                <i className="ri-history-line text-xl ml-2 text-[#3B82F6]"></i>
                النشاطات الأخيرة
              </h2>
              <div className="space-y-4">
                {recentActivities.map((activity) => (
                  <div key={activity.id} className="flex items-start p-4 rounded-xl hover:bg-[#F8FAFC] transition-colors">
                    <div className={`w-10 h-10 ${activity.bgColor} rounded-lg flex items-center justify-center ml-4`}>
                      <i className={`${activity.icon} text-xl ${activity.color}`}></i>
                    </div>
                    <div className="flex-1">
                      <p className="text-[#1E293B] font-medium">{activity.action}</p>
                      <p className="text-[#64748B] text-sm">{activity.item}</p>
                    </div>
                    <span className="text-[#64748B] text-sm">{activity.time}</span>
                  </div>
                ))}
              </div>
            </div>

            {/* Quick Actions */}
            <div className="bg-white rounded-2xl p-6 shadow-[0_0_50px_rgba(0,0,0,0.05)] border border-[#E2E8F0]">
              <h2 className="text-lg font-bold text-[#1E293B] mb-4 flex items-center">
                <i className="ri-flashlight-line text-xl ml-2 text-[#3B82F6]"></i>
                إجراءات سريعة
              </h2>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                {quickActions.map((action, index) => (
                  <a
                    key={index}
                    href={action.href}
                    target={action.external ? '_blank' : undefined}
                    rel={action.external ? 'noopener noreferrer' : undefined}
                    className={`${action.color} p-4 rounded-xl border border-[#E2E8F0] hover:border-[#CBD5E1] transition-all duration-300`}
                  >
                    <div className="flex items-center">
                      <div className={`w-10 h-10 ${action.iconBg} rounded-lg flex items-center justify-center ml-3`}>
                        <i className={`${action.icon} text-xl ${action.iconColor}`}></i>
                      </div>
                      <div>
                        <p className="text-[#1E293B] font-medium">{action.title}</p>
                        <p className="text-[#64748B] text-sm">{action.description}</p>
                      </div>
                    </div>
                  </a>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* Quote Requests Modal */}
        {showQuoteRequests && (
          <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
            <div className="bg-white rounded-2xl w-full max-w-6xl max-h-[90vh] overflow-hidden">
              <div className="p-6 border-b border-[#E2E8F0] flex justify-between items-center">
                <h2 className="text-xl font-bold text-[#1E293B]">طلبات التسعير ({quoteRequests.length})</h2>
                <button
                  onClick={() => setShowQuoteRequests(false)}
                  className="text-[#64748B] hover:text-[#1E293B] transition-colors"
                >
                  <i className="ri-close-line text-2xl"></i>
                </button>
              </div>

              <div className="overflow-auto max-h-[calc(90vh-120px)]">
                {quoteRequests.length === 0 ? (
                  <div className="p-8 text-center">
                    <i className="ri-file-list-line text-4xl text-[#64748B] mb-4"></i>
                    <p className="text-[#64748B]">لا توجد طلبات تسعير</p>
                  </div>
                ) : (
                  <div className="overflow-x-auto">
                    <table className="w-full">
                      <thead className="bg-[#F8FAFC]">
                        <tr>
                          <th className="px-6 py-3 text-right text-xs font-medium text-[#64748B] uppercase tracking-wider">
                            اسم العميل
                          </th>
                          <th className="px-6 py-3 text-right text-xs font-medium text-[#64748B] uppercase tracking-wider">
                            رقم الهاتف
                          </th>
                          <th className="px-6 py-3 text-right text-xs font-medium text-[#64748B] uppercase tracking-wider">
                            البريد الإلكتروني
                          </th>
                          <th className="px-6 py-3 text-right text-xs font-medium text-[#64748B] uppercase tracking-wider">
                            الشركة
                          </th>
                          <th className="px-6 py-3 text-right text-xs font-medium text-[#64748B] uppercase tracking-wider">
                            عدد المنتجات
                          </th>
                          <th className="px-6 py-3 text-right text-xs font-medium text-[#64748B] uppercase tracking-wider">
                            الحالة
                          </th>
                          <th className="px-6 py-3 text-right text-xs font-medium text-[#64748B] uppercase tracking-wider">
                            تاريخ الطلب
                          </th>
                          <th className="px-6 py-3 text-right text-xs font-medium text-[#64748B] uppercase tracking-wider">
                            الإجراءات
                          </th>
                        </tr>
                      </thead>
                      <tbody className="bg-white divide-y divide-[#E2E8F0]">
                        {quoteRequests.map((request) => (
                          <tr key={request.id} className="hover:bg-[#F8FAFC]">
                            <td className="px-6 py-4 whitespace-nowrap">
                              <div className="font-medium text-[#1E293B]">
                                {request.customerInfo.name}
                              </div>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-[#64748B]">
                              {request.customerInfo.phone}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-[#64748B]">
                              {request.customerInfo.email}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-[#64748B]">
                              {request.customerInfo.company || '-'}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-[#64748B]">
                              {request.products.length}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap">
                              <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(request.status)}`}>
                                {request.status === 'pending' ? 'في الانتظار' :
                                 request.status === 'processed' ? 'تم المعالجة' : 'تم الإرسال'}
                              </span>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-[#64748B]">
                              {formatDate(request.createdAt)}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap">
                              <div className="flex gap-2">
                                <button
                                  onClick={() => handleDownloadExcel(request.excelFilePath)}
                                  className="bg-[#059669] text-white px-3 py-1 rounded text-sm hover:bg-[#047857] transition-colors"
                                  title="فتح Excel"
                                >
                                  <i className="ri-file-excel-line"></i>
                                </button>
                                <button
                                  onClick={() => handleSendEmail(request)}
                                  className="bg-[#3B82F6] text-white px-3 py-1 rounded text-sm hover:bg-[#2563EB] transition-colors"
                                  title="إرسال إيميل"
                                >
                                  <i className="ri-mail-line"></i>
                                </button>
                                <button
                                  onClick={() => handleWhatsApp(request)}
                                  className="bg-[#10B981] text-white px-3 py-1 rounded text-sm hover:bg-[#059669] transition-colors"
                                  title="واتساب"
                                >
                                  <i className="ri-whatsapp-line"></i>
                                </button>
                              </div>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                )}
              </div>
            </div>
          </div>
        )}
      </AdminLayout>
    </>
  );
};

export default AdminDashboard;
