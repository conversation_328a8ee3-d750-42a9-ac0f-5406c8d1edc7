import { NextApiRequest, NextApiResponse } from 'next';
import { serialize } from 'cookie';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  // السماح فقط بطلبات POST
  if (req.method !== 'POST') {
    return res.status(405).json({ 
      success: false, 
      message: 'Method not allowed' 
    });
  }
  
  try {
    // حذف cookie المصادقة
    const cookie = serialize('authToken', '', {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
      maxAge: 0, // انتهاء فوري
      path: '/'
    });
    
    res.setHeader('Set-Cookie', cookie);
    
    res.status(200).json({
      success: true,
      message: 'Logout successful',
      messageAr: 'تم تسجيل الخروج بنجاح'
    });
    
  } catch (error) {
    console.error('Logout API error:', error);
    
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      messageAr: 'خطأ في الخادم الداخلي'
    });
  }
}
