import React from 'react';
import { useTranslation } from 'next-i18next';

const PartnersSection = () => {
  const { t } = useTranslation('common');

  const partners = [
    { icon: 'ri-hotel-line', name: t('partners.riyadhHotel') },
    { icon: 'ri-hotel-line', name: t('partners.jeddahHotel') },
    { icon: 'ri-restaurant-2-line', name: t('partners.eastRestaurant') },
    { icon: 'ri-hotel-line', name: t('partners.dammanHotel') },
    { icon: 'ri-restaurant-2-line', name: t('partners.originalRestaurant') },
    { icon: 'ri-hotel-line', name: t('partners.madinahHotel') },
  ];
  return (
    <section className="py-16 bg-white">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold text-primary mb-4">{t('partners.title')}</h2>
          <p className="text-gray-600 max-w-2xl mx-auto">
            {t('partners.subtitle')}
          </p>
        </div>
        <div className="overflow-hidden">
          <div className="flex gap-12 items-center justify-center min-w-full partners-slider">
            {partners.map((p, idx) => (
              <div className="w-32 h-32 flex items-center justify-center" key={idx}>
                <div className="text-center">
                  <div className="w-16 h-16 mx-auto flex items-center justify-center text-primary">
                    <i className={`${p.icon} ri-3x`}></i>
                  </div>
                  <p className="mt-2 font-medium">{p.name}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};

export default PartnersSection;
