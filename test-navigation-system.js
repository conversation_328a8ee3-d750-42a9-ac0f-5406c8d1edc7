// اختبار نظام التنقل الجديد
const baseUrl = 'http://localhost:3000';

async function testNavigationFlow() {
  console.log('🧪 اختبار نظام التنقل الجديد...\n');

  try {
    // 1. اختبار جلب الفئات الرئيسية
    console.log('1. اختبار جلب الفئات الرئيسية:');
    const categoriesResponse = await fetch(`${baseUrl}/api/categories`);
    if (categoriesResponse.ok) {
      const categories = await categoriesResponse.json();
      console.log(`✅ تم جلب ${categories.length} فئة رئيسية`);
      
      if (categories.length > 0) {
        const firstCategory = categories[0];
        console.log(`📁 الفئة الأولى: ${firstCategory.name_ar} (${firstCategory.id})`);
        
        // 2. اختبار جلب الفئات الفرعية للفئة الأولى
        console.log('\n2. اختبار جلب الفئات الفرعية:');
        const subcategoriesResponse = await fetch(`${baseUrl}/api/subcategories`);
        if (subcategoriesResponse.ok) {
          const allSubcategories = await subcategoriesResponse.json();
          const categorySubcategories = allSubcategories.filter(sub => sub.category_id === firstCategory.id);
          console.log(`✅ تم جلب ${categorySubcategories.length} فئة فرعية للفئة الأولى`);
          
          if (categorySubcategories.length > 0) {
            const firstSubcategory = categorySubcategories[0];
            console.log(`📂 الفئة الفرعية الأولى: ${firstSubcategory.name_ar} (${firstSubcategory.id})`);
            
            // 3. اختبار جلب المنتجات للفئة الفرعية
            console.log('\n3. اختبار جلب منتجات الفئة الفرعية:');
            const productsResponse = await fetch(`${baseUrl}/api/products?subcategoryId=${firstSubcategory.id}`);
            if (productsResponse.ok) {
              const products = await productsResponse.json();
              console.log(`✅ تم جلب ${products.length} منتج للفئة الفرعية`);
              
              if (products.length > 0) {
                const firstProduct = products[0];
                console.log(`📦 المنتج الأول: ${firstProduct.title_ar} (${firstProduct.id})`);
                
                // 4. اختبار جلب تفاصيل المنتج
                console.log('\n4. اختبار جلب تفاصيل المنتج:');
                const productResponse = await fetch(`${baseUrl}/api/products/${firstProduct.id}`);
                if (productResponse.ok) {
                  const productDetails = await productResponse.json();
                  console.log(`✅ تم جلب تفاصيل المنتج بنجاح`);
                  console.log(`   - العنوان: ${productDetails.title_ar}`);
                  console.log(`   - السعر: ${productDetails.price} ر.س`);
                  console.log(`   - عدد الصور: ${productDetails.images?.length || 0}`);
                  console.log(`   - عدد المميزات: ${productDetails.features?.length || 0}`);
                  console.log(`   - عدد المواصفات: ${productDetails.specifications?.length || 0}`);
                } else {
                  console.log('❌ فشل في جلب تفاصيل المنتج');
                }
              } else {
                console.log('⚠️ لا توجد منتجات في هذه الفئة الفرعية');
              }
            } else {
              console.log('❌ فشل في جلب منتجات الفئة الفرعية');
            }
          } else {
            console.log('⚠️ لا توجد فئات فرعية لهذه الفئة');
          }
        } else {
          console.log('❌ فشل في جلب الفئات الفرعية');
        }
      } else {
        console.log('⚠️ لا توجد فئات رئيسية');
      }
    } else {
      console.log('❌ فشل في جلب الفئات الرئيسية');
    }
  } catch (error) {
    console.error('❌ خطأ في اختبار نظام التنقل:', error.message);
  }
}

async function testPageAccess() {
  console.log('\n🌐 اختبار الوصول للصفحات الجديدة...\n');

  // جلب بيانات للاختبار
  let categoryId = null;
  let subcategoryId = null;
  let productId = null;

  try {
    // جلب فئة للاختبار
    const categoriesResponse = await fetch(`${baseUrl}/api/categories`);
    if (categoriesResponse.ok) {
      const categories = await categoriesResponse.json();
      if (categories.length > 0) {
        categoryId = categories[0].id;
      }
    }

    // جلب فئة فرعية للاختبار
    const subcategoriesResponse = await fetch(`${baseUrl}/api/subcategories`);
    if (subcategoriesResponse.ok) {
      const subcategories = await subcategoriesResponse.json();
      if (subcategories.length > 0) {
        subcategoryId = subcategories[0].id;
      }
    }

    // جلب منتج للاختبار
    const productsResponse = await fetch(`${baseUrl}/api/products`);
    if (productsResponse.ok) {
      const products = await productsResponse.json();
      if (products.length > 0) {
        productId = products[0].id;
      }
    }
  } catch (error) {
    console.log('⚠️ خطأ في جلب البيانات للاختبار:', error.message);
  }

  const pages = [
    { name: 'الصفحة الرئيسية', url: `${baseUrl}/ar` },
    { name: 'صفحة المنتجات', url: `${baseUrl}/ar/products` },
  ];

  // إضافة صفحات ديناميكية إذا توفرت البيانات
  if (categoryId) {
    pages.push({ name: 'صفحة الفئة الرئيسية', url: `${baseUrl}/ar/category/${categoryId}` });
  }
  if (subcategoryId) {
    pages.push({ name: 'صفحة الفئة الفرعية', url: `${baseUrl}/ar/subcategory/${subcategoryId}` });
  }
  if (productId) {
    pages.push({ name: 'صفحة تفاصيل المنتج', url: `${baseUrl}/ar/product/${productId}` });
  }

  for (const page of pages) {
    try {
      console.log(`📄 اختبار: ${page.name}`);
      const response = await fetch(page.url);
      console.log(`   Status: ${response.status}`);
      console.log(`   نتيجة: ${response.ok ? '✅ تحمل بنجاح' : '❌ فشل في التحميل'}`);
      
      if (response.ok) {
        console.log(`   🔗 يمكنك زيارة: ${page.url}`);
      }
    } catch (error) {
      console.log(`   ❌ خطأ: ${error.message}`);
    }
    console.log('');
  }
}

async function testNavigationLinks() {
  console.log('🔗 اختبار روابط التنقل...\n');

  try {
    // جلب البيانات
    const categoriesResponse = await fetch(`${baseUrl}/api/categories`);
    const subcategoriesResponse = await fetch(`${baseUrl}/api/subcategories`);
    const productsResponse = await fetch(`${baseUrl}/api/products`);

    if (categoriesResponse.ok && subcategoriesResponse.ok && productsResponse.ok) {
      const categories = await categoriesResponse.json();
      const subcategories = await subcategoriesResponse.json();
      const products = await productsResponse.json();

      console.log('📊 إحصائيات البيانات:');
      console.log(`   - الفئات الرئيسية: ${categories.length}`);
      console.log(`   - الفئات الفرعية: ${subcategories.length}`);
      console.log(`   - المنتجات: ${products.length}`);

      // تحليل العلاقات
      console.log('\n🔍 تحليل العلاقات:');
      categories.forEach(category => {
        const categorySubcategories = subcategories.filter(sub => sub.category_id === category.id);
        console.log(`   📁 ${category.name_ar}: ${categorySubcategories.length} فئة فرعية`);
        
        categorySubcategories.forEach(subcategory => {
          const subcategoryProducts = products.filter(prod => prod.subcategory_id === subcategory.id);
          console.log(`      📂 ${subcategory.name_ar}: ${subcategoryProducts.length} منتج`);
        });
      });

      // منتجات بدون فئة فرعية
      const productsWithoutSubcategory = products.filter(prod => !prod.subcategory_id);
      if (productsWithoutSubcategory.length > 0) {
        console.log(`\n⚠️ منتجات بدون فئة فرعية: ${productsWithoutSubcategory.length}`);
      }
    }
  } catch (error) {
    console.error('❌ خطأ في تحليل روابط التنقل:', error.message);
  }
}

async function main() {
  console.log('🚀 بدء اختبار نظام التنقل الجديد\n');
  
  await testNavigationFlow();
  await testPageAccess();
  await testNavigationLinks();
  
  console.log('\n📋 ملخص نظام التنقل الجديد:');
  console.log('1. ✅ الفئة الرئيسية → عرض الفئات الفرعية');
  console.log('2. ✅ الفئة الفرعية → عرض المنتجات المفلترة');
  console.log('3. ✅ المنتج → عرض تفاصيل المنتج الكاملة');
  console.log('4. ✅ Breadcrumb navigation في جميع الصفحات');
  console.log('5. ✅ روابط العودة والتنقل');
  
  console.log('\n🎯 مسار التنقل المتوقع:');
  console.log('الصفحة الرئيسية → فئة رئيسية → فئة فرعية → منتج → تفاصيل المنتج');
  
  console.log('\n📱 الصفحات المتاحة:');
  console.log('- /ar - الصفحة الرئيسية (عرض الفئات الرئيسية)');
  console.log('- /ar/products - جميع المنتجات');
  console.log('- /ar/category/[id] - الفئات الفرعية للفئة الرئيسية');
  console.log('- /ar/subcategory/[id] - منتجات الفئة الفرعية');
  console.log('- /ar/product/[id] - تفاصيل المنتج');
  
  console.log('\n🏁 انتهاء اختبار نظام التنقل');
}

main();
