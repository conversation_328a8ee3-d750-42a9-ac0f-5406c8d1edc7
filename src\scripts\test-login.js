const bcrypt = require('bcryptjs');
const fs = require('fs');
const path = require('path');

async function testLogin() {
  try {
    // قراءة قاعدة البيانات
    const DB_PATH = path.join(process.cwd(), 'src/data/database.json');
    const database = JSON.parse(fs.readFileSync(DB_PATH, 'utf8'));
    const adminUser = database.adminUser;
    
    console.log('🔍 اختبار نظام تسجيل الدخول');
    console.log('================================');
    console.log('اسم المستخدم في قاعدة البيانات:', adminUser.username);
    console.log('كلمة المرور المشفرة:', adminUser.password);
    
    // اختبار كلمات مرور مختلفة
    const passwords = [
      'admin123',
      'Droob<PERSON><PERSON><PERSON>@2024!SecureAdminPassword#VeryLong&Complex',
      'admin',
      'password'
    ];
    
    console.log('\n🧪 اختبار كلمات المرور:');
    console.log('========================');
    
    for (const password of passwords) {
      const isValid = await bcrypt.compare(password, adminUser.password);
      console.log(`كلمة المرور: "${password}" -> ${isValid ? '✅ صحيحة' : '❌ خاطئة'}`);
    }
    
    // إنشاء كلمة مرور جديدة بسيطة
    console.log('\n🔐 إنشاء كلمة مرور جديدة:');
    console.log('============================');
    const newPassword = 'admin123';
    const newHash = await bcrypt.hash(newPassword, 12);
    console.log('كلمة المرور الجديدة:', newPassword);
    console.log('الـ Hash الجديد:', newHash);
    
    // تحديث قاعدة البيانات
    adminUser.password = newHash;
    adminUser.updatedAt = new Date().toISOString();
    fs.writeFileSync(DB_PATH, JSON.stringify(database, null, 2));
    
    console.log('\n✅ تم تحديث كلمة المرور في قاعدة البيانات!');
    console.log('يمكنك الآن تسجيل الدخول باستخدام:');
    console.log('اسم المستخدم: admin');
    console.log('كلمة المرور: admin123');
    
  } catch (error) {
    console.error('❌ خطأ:', error);
  }
}

testLogin();
