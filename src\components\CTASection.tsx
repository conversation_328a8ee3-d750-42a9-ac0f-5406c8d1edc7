import React from 'react';
import Link from 'next/link';
import { useTranslation } from 'next-i18next';

const CTASection = () => {
  const { t } = useTranslation('common');
  return (
    <section className="py-16 bg-gray-50">
      <div className="container mx-auto px-4">
        <div className="bg-primary rounded-lg p-8 md:p-12 text-center text-white">
          <h2 className="text-3xl font-bold mb-6">
            {t('cta.title')}
          </h2>
          <p className="text-lg mb-8 max-w-2xl mx-auto">
            {t('cta.description')}
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              href="/contact"
              className="bg-white text-primary px-8 py-3 rounded-button font-medium hover:bg-gray-100 transition-colors whitespace-nowrap"
            >
              {t('cta.contact')}
            </Link>
            <a
              href="https://wa.me/966500000000"
              className="bg-green-600 text-white px-8 py-3 rounded-button font-medium hover:bg-green-700 transition-colors flex items-center justify-center gap-2 whitespace-nowrap"
              target="_blank"
              rel="noopener noreferrer"
            >
              <i className="ri-whatsapp-line"></i>
              <span>{t('cta.whatsapp')}</span>
            </a>
          </div>
        </div>
      </div>
    </section>
  );
};

export default CTASection;
