'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { Locale } from '../lib/i18n';
import { ProductWithDetails } from '../src/types/mysql-database';
import ProductCard from './ProductCard';

interface FeaturedProductsProps {
  locale: Locale;
}

const FeaturedProducts: React.FC<FeaturedProductsProps> = ({ locale }) => {
  const [products, setProducts] = useState<ProductWithDetails[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchFeaturedProducts = async () => {
      try {
        const response = await fetch('/api/products?featured=true');
        if (response.ok) {
          const data = await response.json();
          // أخذ أول 6 منتجات مميزة
          setProducts((data || []).slice(0, 6));
        } else {
          console.error('Failed to fetch featured products:', response.status);
          setProducts([]);
        }
      } catch (error) {
        console.error('Error fetching featured products:', error);
        setProducts([]);
      } finally {
        setLoading(false);
      }
    };

    fetchFeaturedProducts();
  }, []);

  if (loading) {
    return (
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-gray-600">
              {locale === 'ar' ? 'جاري تحميل المنتجات المميزة...' : 'Loading featured products...'}
            </p>
          </div>
        </div>
      </section>
    );
  }

  if (products.length === 0) {
    return null;
  }

  return (
    <section className="py-24 bg-gradient-to-br from-white via-gray-50 to-primary-50 relative overflow-hidden">
      {/* Background decorative elements */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute top-10 right-10 w-40 h-40 bg-primary rounded-full animate-float"></div>
        <div className="absolute bottom-10 left-10 w-32 h-32 bg-secondary rounded-full animate-float" style={{animationDelay: '1.5s'}}></div>
        <div className="absolute top-1/3 left-1/3 w-24 h-24 bg-accent rounded-full animate-float" style={{animationDelay: '0.8s'}}></div>
      </div>

      <div className="container mx-auto px-4 relative z-10">
        <div className="text-center mb-16">
          <div className="inline-block mb-6">
            <span className="bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent text-sm font-bold uppercase tracking-wider">
              {locale === 'ar' ? 'منتجاتنا المختارة' : 'Our Selected Products'}
            </span>
          </div>
          <h2 className="text-5xl md:text-6xl font-bold bg-gradient-to-r from-dark-800 via-primary-600 to-dark-800 bg-clip-text text-transparent mb-6 leading-tight">
            {locale === 'ar' ? 'المنتجات المميزة' : 'Featured Products'}
          </h2>
          <div className="w-24 h-1 bg-gradient-to-r from-primary to-secondary mx-auto mb-6 rounded-full"></div>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            {locale === 'ar'
              ? 'اكتشف أفضل منتجاتنا المختارة بعناية لتلبية احتياجاتك مع أعلى معايير الجودة والأداء'
              : 'Discover our best products carefully selected to meet your needs with the highest standards of quality and performance'
            }
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
          {products.map((product) => (
            <ProductCard
              key={product.id}
              id={parseInt(product.id)}
              image={product.images?.[0]?.image_url || '/api/placeholder?width=400&height=300&text=لا توجد صورة'}
              title={locale === 'ar' ? product.title_ar : product.title}
              description={locale === 'ar' ? product.description_ar : product.description}
              price={product.price}
              available={product.is_available}
              locale={locale}
            />
          ))}
        </div>

        <div className="text-center mt-16">
          <div className="bg-white rounded-3xl p-8 shadow-glow border border-gray-100 max-w-2xl mx-auto">
            <div className="w-16 h-16 bg-gradient-to-br from-primary to-secondary rounded-2xl flex items-center justify-center mx-auto mb-6 animate-glow">
              <i className="ri-store-2-line text-3xl text-white"></i>
            </div>
            <h3 className="text-2xl font-bold text-gray-800 mb-4">
              {locale === 'ar' ? 'استكشف المزيد من المنتجات' : 'Explore More Products'}
            </h3>
            <p className="text-gray-600 mb-6">
              {locale === 'ar'
                ? 'تصفح مجموعتنا الكاملة من المعدات عالية الجودة'
                : 'Browse our complete collection of high-quality equipment'
              }
            </p>
            <Link
              href={`/${locale}/products`}
              className="btn-modern gradient-primary text-white px-10 py-4 rounded-2xl font-bold text-lg transition-all duration-500 transform hover:scale-105 hover:shadow-glow-lg inline-flex items-center gap-3"
            >
              <i className="ri-shopping-bag-line text-xl"></i>
              <span>{locale === 'ar' ? 'عرض جميع المنتجات' : 'View All Products'}</span>
              <i className="ri-arrow-right-line transition-transform duration-300 group-hover:translate-x-2"></i>
            </Link>
          </div>
        </div>
      </div>
    </section>
  );
};

export default FeaturedProducts;
