import CryptoJS from 'crypto-js';
import fs from 'fs';
import path from 'path';

// مفتاح التشفير لقاعدة البيانات
const DB_ENCRYPTION_KEY = process.env.DB_ENCRYPTION_KEY || process.env.ENCRYPTION_KEY || 'your-database-encryption-key';

// مسارات الملفات
const ORIGINAL_DB_PATH = path.join(process.cwd(), 'src', 'data', 'database.json');
const ENCRYPTED_DB_PATH = path.join(process.cwd(), 'data', 'encrypted-database.enc');
const BACKUP_DB_PATH = path.join(process.cwd(), 'data', 'database-backup.json');

// إنشاء مجلد البيانات المشفرة
function ensureDataDirectory(): void {
  const dataDir = path.dirname(ENCRYPTED_DB_PATH);
  if (!fs.existsSync(dataDir)) {
    fs.mkdirSync(dataDir, { recursive: true });
  }
}

// تشفير البيانات
function encryptDatabase(data: any): string {
  try {
    const jsonString = JSON.stringify(data, null, 2);
    const encrypted = CryptoJS.AES.encrypt(jsonString, DB_ENCRYPTION_KEY).toString();
    return encrypted;
  } catch (error) {
    console.error('Error encrypting database:', error);
    throw new Error('Failed to encrypt database');
  }
}

// فك تشفير البيانات
function decryptDatabase(encryptedData: string): any {
  try {
    const decrypted = CryptoJS.AES.decrypt(encryptedData, DB_ENCRYPTION_KEY);
    const jsonString = decrypted.toString(CryptoJS.enc.Utf8);
    
    if (!jsonString) {
      throw new Error('Failed to decrypt - invalid key or corrupted data');
    }
    
    return JSON.parse(jsonString);
  } catch (error) {
    console.error('Error decrypting database:', error);
    throw new Error('Failed to decrypt database');
  }
}

// قراءة قاعدة البيانات المشفرة
export function readEncryptedDatabase(): any {
  try {
    ensureDataDirectory();
    
    // إذا لم يكن الملف المشفر موجوداً، قم بإنشائه من الملف الأصلي
    if (!fs.existsSync(ENCRYPTED_DB_PATH)) {
      return migrateToEncryptedDatabase();
    }
    
    const encryptedData = fs.readFileSync(ENCRYPTED_DB_PATH, 'utf8');
    return decryptDatabase(encryptedData);
  } catch (error) {
    console.error('Error reading encrypted database:', error);
    
    // في حالة الخطأ، حاول قراءة النسخة الاحتياطية
    if (fs.existsSync(BACKUP_DB_PATH)) {
      console.log('Attempting to restore from backup...');
      const backupData = JSON.parse(fs.readFileSync(BACKUP_DB_PATH, 'utf8'));
      writeEncryptedDatabase(backupData);
      return backupData;
    }
    
    throw new Error('Failed to read database and no backup available');
  }
}

// كتابة قاعدة البيانات المشفرة
export function writeEncryptedDatabase(data: any): void {
  try {
    ensureDataDirectory();
    
    // إنشاء نسخة احتياطية قبل الكتابة
    if (fs.existsSync(ENCRYPTED_DB_PATH)) {
      const currentData = readEncryptedDatabase();
      fs.writeFileSync(BACKUP_DB_PATH, JSON.stringify(currentData, null, 2));
    }
    
    const encryptedData = encryptDatabase(data);
    fs.writeFileSync(ENCRYPTED_DB_PATH, encryptedData, 'utf8');
  } catch (error) {
    console.error('Error writing encrypted database:', error);
    throw new Error('Failed to write encrypted database');
  }
}

// ترحيل قاعدة البيانات إلى النظام المشفر
export function migrateToEncryptedDatabase(): any {
  try {
    console.log('🔄 Migrating database to encrypted format...');
    
    if (!fs.existsSync(ORIGINAL_DB_PATH)) {
      throw new Error('Original database file not found');
    }
    
    // قراءة قاعدة البيانات الأصلية
    const originalData = JSON.parse(fs.readFileSync(ORIGINAL_DB_PATH, 'utf8'));
    
    // إزالة البيانات الحساسة إذا كانت موجودة
    if (originalData.adminUser) {
      console.log('⚠️  Removing sensitive admin data from database...');
      delete originalData.adminUser;
    }
    
    // تشفير وحفظ البيانات
    writeEncryptedDatabase(originalData);
    
    console.log('✅ Database migration completed successfully');
    return originalData;
  } catch (error) {
    console.error('❌ Error migrating database:', error);
    throw error;
  }
}

// تحديث قاعدة البيانات المشفرة
export function updateEncryptedDatabase(updates: any): any {
  try {
    const currentData = readEncryptedDatabase();
    const updatedData = { ...currentData, ...updates };
    writeEncryptedDatabase(updatedData);
    return updatedData;
  } catch (error) {
    console.error('Error updating encrypted database:', error);
    throw new Error('Failed to update database');
  }
}

// إنشاء نسخة احتياطية يدوية
export function createDatabaseBackup(): string {
  try {
    const data = readEncryptedDatabase();
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const backupPath = path.join(process.cwd(), 'data', `backup-${timestamp}.json`);
    
    fs.writeFileSync(backupPath, JSON.stringify(data, null, 2));
    console.log(`✅ Database backup created: ${backupPath}`);
    
    return backupPath;
  } catch (error) {
    console.error('Error creating database backup:', error);
    throw new Error('Failed to create backup');
  }
}

// استعادة من نسخة احتياطية
export function restoreFromBackup(backupPath: string): void {
  try {
    if (!fs.existsSync(backupPath)) {
      throw new Error('Backup file not found');
    }
    
    const backupData = JSON.parse(fs.readFileSync(backupPath, 'utf8'));
    writeEncryptedDatabase(backupData);
    
    console.log('✅ Database restored from backup successfully');
  } catch (error) {
    console.error('Error restoring from backup:', error);
    throw new Error('Failed to restore from backup');
  }
}

// التحقق من سلامة قاعدة البيانات
export function verifyDatabaseIntegrity(): boolean {
  try {
    const data = readEncryptedDatabase();
    
    // فحوصات أساسية
    if (!data || typeof data !== 'object') {
      return false;
    }
    
    // التحقق من وجود الأقسام الأساسية
    const requiredSections = ['categories', 'subcategories', 'products', 'siteSettings'];
    for (const section of requiredSections) {
      if (!data[section]) {
        console.warn(`⚠️  Missing required section: ${section}`);
        return false;
      }
    }
    
    console.log('✅ Database integrity check passed');
    return true;
  } catch (error) {
    console.error('❌ Database integrity check failed:', error);
    return false;
  }
}

// تنظيف الملفات القديمة
export function cleanupOldFiles(): void {
  try {
    // حذف الملف الأصلي بعد التأكد من نجاح الترحيل
    if (fs.existsSync(ENCRYPTED_DB_PATH) && verifyDatabaseIntegrity()) {
      if (fs.existsSync(ORIGINAL_DB_PATH)) {
        // إنشاء نسخة احتياطية أخيرة قبل الحذف
        const backupPath = path.join(path.dirname(ORIGINAL_DB_PATH), 'database-original-backup.json');
        fs.copyFileSync(ORIGINAL_DB_PATH, backupPath);
        
        // حذف الملف الأصلي
        fs.unlinkSync(ORIGINAL_DB_PATH);
        console.log('✅ Original database file removed (backup created)');
      }
    }
  } catch (error) {
    console.error('Error cleaning up old files:', error);
  }
}
