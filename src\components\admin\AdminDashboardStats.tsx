import React, { useState, useEffect } from 'react';
import StatsCard from './StatsCard';

interface AdminDashboardStatsProps {
  className?: string;
}

interface AdminStats {
  products: {
    total: number;
    active: number;
    featured: number;
    added_this_week: number;
    updated_today: number;
  };
  categories: {
    total: number;
    active: number;
  };
  admins: {
    total: number;
    active_this_week: number;
    new_this_month: number;
  };
  quotes: {
    total: number;
    pending: number;
    today: number;
    this_week: number;
  };
  system: {
    database_size: number;
    uploads_size: number;
    total_files: number;
  };
  recent_products: Array<{
    id: number;
    name_ar: string;
    name_en: string;
    is_active: number;
    is_featured: number;
    created_at: string;
  }>;
  last_updated: string;
}

const AdminDashboardStats: React.FC<AdminDashboardStatsProps> = ({ className = '' }) => {
  const [stats, setStats] = useState<AdminStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchStats = async () => {
    try {
      setLoading(true);
      setError(null);
      
      // الحصول على التوكن
      const token = localStorage.getItem('authToken') || 
                   document.cookie.split('; ').find(row => row.startsWith('authToken='))?.split('=')[1];

      const response = await fetch('/api/admin-dashboard-stats', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        credentials: 'include'
      });
      
      const data = await response.json();
      
      if (data.success) {
        setStats(data.stats);
      } else {
        setError(data.messageAr || data.message || 'فشل في جلب الإحصائيات');
      }
    } catch (error) {
      console.error('Error fetching admin dashboard stats:', error);
      setError('خطأ في الشبكة');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchStats();
    
    // تحديث الإحصائيات كل 5 دقائق
    const interval = setInterval(fetchStats, 5 * 60 * 1000);
    
    return () => clearInterval(interval);
  }, []);

  if (loading) {
    return (
      <div className={`space-y-6 ${className}`}>
        <div className="flex items-center justify-between">
          <h2 className="text-xl font-bold text-gray-800">إحصائيات سريعة</h2>
          <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {[...Array(8)].map((_, i) => (
            <div key={i} className="bg-gray-100 rounded-xl p-6 animate-pulse">
              <div className="h-4 bg-gray-200 rounded mb-2"></div>
              <div className="h-8 bg-gray-200 rounded mb-1"></div>
              <div className="h-3 bg-gray-200 rounded w-2/3"></div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`bg-red-50 border border-red-200 rounded-xl p-6 text-center ${className}`}>
        <i className="ri-error-warning-line text-red-500 text-3xl mb-2"></i>
        <p className="text-red-700 mb-4">خطأ في تحميل الإحصائيات: {error}</p>
        <button
          onClick={fetchStats}
          className="bg-red-500 text-white px-4 py-2 rounded-lg hover:bg-red-600 transition-colors"
        >
          إعادة المحاولة
        </button>
      </div>
    );
  }

  if (!stats) return null;

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <h2 className="text-xl font-bold text-gray-800">إحصائيات سريعة</h2>
        <div className="flex items-center gap-4">
          <span className="text-sm text-gray-500">
            آخر تحديث: {new Date(stats.last_updated).toLocaleTimeString('ar-SA')}
          </span>
          <button
            onClick={fetchStats}
            className="bg-primary text-white px-3 py-1 rounded-lg hover:bg-primary/90 transition-colors text-sm flex items-center gap-1"
          >
            <i className="ri-refresh-line"></i>
            تحديث
          </button>
        </div>
      </div>

      {/* Main Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatsCard
          title="إجمالي المنتجات"
          value={stats.products.total}
          icon="ri-shopping-bag-line"
          color="blue"
          subtitle={`${stats.products.active} نشط`}
        />
        
        <StatsCard
          title="المنتجات المميزة"
          value={stats.products.featured}
          icon="ri-star-line"
          color="yellow"
          subtitle="منتجات مميزة"
        />
        
        <StatsCard
          title="إجمالي الفئات"
          value={stats.categories.total}
          icon="ri-folder-line"
          color="green"
          subtitle={`${stats.categories.active} نشط`}
        />
        
        <StatsCard
          title="طلبات التسعير"
          value={stats.quotes.total}
          icon="ri-file-list-line"
          color="purple"
          subtitle={`${stats.quotes.pending} معلق`}
        />
      </div>

      {/* Activity Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatsCard
          title="منتجات هذا الأسبوع"
          value={stats.products.added_this_week}
          icon="ri-add-circle-line"
          color="green"
          subtitle="منتجات جديدة"
        />
        
        <StatsCard
          title="تحديثات اليوم"
          value={stats.products.updated_today}
          icon="ri-edit-line"
          color="blue"
          subtitle="منتجات محدثة"
        />
        
        <StatsCard
          title="طلبات اليوم"
          value={stats.quotes.today}
          icon="ri-calendar-today-line"
          color="indigo"
          subtitle="طلبات جديدة"
        />
        
        <StatsCard
          title="المديرين النشطين"
          value={stats.admins.active_this_week}
          icon="ri-user-line"
          color="purple"
          subtitle="هذا الأسبوع"
        />
      </div>

      {/* System Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <StatsCard
          title="حجم قاعدة البيانات"
          value={`${stats.system.database_size} MB`}
          icon="ri-database-line"
          color="red"
          subtitle="مساحة مستخدمة"
        />
        
        <StatsCard
          title="مساحة الرفع"
          value={`${stats.system.uploads_size} MB`}
          icon="ri-hard-drive-line"
          color="yellow"
          subtitle={`${stats.system.total_files} ملف`}
        />
        
        <StatsCard
          title="إجمالي المديرين"
          value={stats.admins.total}
          icon="ri-admin-line"
          color="indigo"
          subtitle={`${stats.admins.new_this_month} جديد هذا الشهر`}
        />
      </div>

      {/* Recent Products */}
      {stats.recent_products.length > 0 && (
        <div className="bg-white rounded-xl shadow-lg p-6">
          <h3 className="text-lg font-bold text-gray-800 mb-4">أحدث المنتجات المضافة</h3>
          <div className="space-y-3">
            {stats.recent_products.map((product) => (
              <div key={product.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div className="flex-1">
                  <h4 className="font-medium text-gray-800">{product.name_ar}</h4>
                  <p className="text-sm text-gray-600">{product.name_en}</p>
                </div>
                <div className="flex items-center gap-2">
                  {product.is_featured === 1 && (
                    <span className="bg-yellow-100 text-yellow-800 px-2 py-1 rounded-full text-xs">
                      مميز
                    </span>
                  )}
                  <span className={`px-2 py-1 rounded-full text-xs ${
                    product.is_active === 1 
                      ? 'bg-green-100 text-green-800' 
                      : 'bg-red-100 text-red-800'
                  }`}>
                    {product.is_active === 1 ? 'نشط' : 'غير نشط'}
                  </span>
                  <span className="text-xs text-gray-500">
                    {new Date(product.created_at).toLocaleDateString('ar-SA')}
                  </span>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default AdminDashboardStats;
