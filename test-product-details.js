// اختبار صفحة تفاصيل المنتج
const baseUrl = 'http://localhost:3000';

async function testProductDetailsPage() {
  console.log('🧪 اختبار صفحة تفاصيل المنتج...\n');

  try {
    // جلب منتج للاختبار
    const productsResponse = await fetch(`${baseUrl}/api/products`);
    if (productsResponse.ok) {
      const products = await productsResponse.json();
      if (products.length > 0) {
        const testProduct = products[0];
        console.log(`📦 منتج الاختبار: ${testProduct.title_ar}`);
        console.log(`🆔 ID: ${testProduct.id}`);

        // اختبار API تفاصيل المنتج
        console.log('\n1. اختبار API تفاصيل المنتج:');
        const apiResponse = await fetch(`${baseUrl}/api/products/${testProduct.id}`);
        console.log(`   Status: ${apiResponse.status}`);
        
        if (apiResponse.ok) {
          const productDetails = await apiResponse.json();
          console.log('   ✅ API يعمل بنجاح');
          console.log(`   📊 تفاصيل المنتج:`);
          console.log(`      - العنوان: ${productDetails.title_ar}`);
          console.log(`      - الوصف: ${productDetails.description_ar?.substring(0, 50)}...`);
          console.log(`      - السعر: ${productDetails.price} ر.س`);
          console.log(`      - متوفر: ${productDetails.is_available ? 'نعم' : 'لا'}`);
          console.log(`      - عدد الصور: ${productDetails.images?.length || 0}`);
          console.log(`      - عدد المميزات: ${productDetails.features?.length || 0}`);
          console.log(`      - عدد المواصفات: ${productDetails.specifications?.length || 0}`);
          
          // عرض تفاصيل الصور
          if (productDetails.images && productDetails.images.length > 0) {
            console.log(`   🖼️ الصور:`);
            productDetails.images.forEach((img, index) => {
              console.log(`      ${index + 1}. ${img.image_url}`);
            });
          }
          
          // عرض المميزات
          if (productDetails.features && productDetails.features.length > 0) {
            console.log(`   ⭐ المميزات:`);
            productDetails.features.forEach((feature, index) => {
              console.log(`      ${index + 1}. ${feature.feature_text_ar}`);
            });
          }
          
          // عرض المواصفات
          if (productDetails.specifications && productDetails.specifications.length > 0) {
            console.log(`   📋 المواصفات:`);
            productDetails.specifications.forEach((spec, index) => {
              console.log(`      ${index + 1}. ${spec.spec_key_ar}: ${spec.spec_value_ar}`);
            });
          }
        } else {
          console.log('   ❌ API لا يعمل');
          const errorText = await apiResponse.text();
          console.log(`   خطأ: ${errorText.substring(0, 200)}...`);
        }

        // اختبار صفحة تفاصيل المنتج
        console.log('\n2. اختبار صفحة تفاصيل المنتج:');
        const pageResponse = await fetch(`${baseUrl}/product-details/${testProduct.id}`);
        console.log(`   Status: ${pageResponse.status}`);
        
        if (pageResponse.ok) {
          console.log('   ✅ الصفحة تحمل بنجاح');
          console.log(`   🔗 يمكنك زيارة: ${baseUrl}/product-details/${testProduct.id}`);
        } else {
          console.log('   ❌ الصفحة لا تحمل');
          const errorText = await pageResponse.text();
          console.log(`   خطأ: ${errorText.substring(0, 200)}...`);
        }

        // اختبار الصور في الصفحة
        console.log('\n3. اختبار الصور:');
        if (testProduct.images && testProduct.images.length > 0) {
          for (let i = 0; i < Math.min(2, testProduct.images.length); i++) {
            const image = testProduct.images[i];
            console.log(`   🖼️ صورة ${i + 1}: ${image.image_url}`);
            
            try {
              const imageResponse = await fetch(`${baseUrl}${image.image_url}`);
              console.log(`      Status: ${imageResponse.status} ${imageResponse.ok ? '✅' : '❌'}`);
              console.log(`      Content-Type: ${imageResponse.headers.get('content-type')}`);
            } catch (error) {
              console.log(`      ❌ خطأ: ${error.message}`);
            }
          }
        } else {
          console.log('   ⚠️ لا توجد صور للاختبار');
        }

      } else {
        console.log('⚠️ لا توجد منتجات للاختبار');
      }
    } else {
      console.log('❌ فشل في جلب المنتجات');
    }
  } catch (error) {
    console.error('❌ خطأ في اختبار صفحة تفاصيل المنتج:', error.message);
  }
}

async function testProductCardLinks() {
  console.log('\n🔗 اختبار روابط ProductCard...\n');

  try {
    // جلب منتج للاختبار
    const productsResponse = await fetch(`${baseUrl}/api/products`);
    if (productsResponse.ok) {
      const products = await productsResponse.json();
      if (products.length > 0) {
        const testProduct = products[0];
        
        console.log('📋 اختبار الروابط:');
        console.log(`   المنتج: ${testProduct.title_ar}`);
        console.log(`   ID: ${testProduct.id}`);
        
        // الرابط الجديد (المفترض أن يعمل)
        const newLink = `/product-details/${testProduct.id}`;
        console.log(`\n   🔗 الرابط الجديد: ${newLink}`);
        const newResponse = await fetch(`${baseUrl}${newLink}`);
        console.log(`      Status: ${newResponse.status} ${newResponse.ok ? '✅' : '❌'}`);
        
        // الرابط القديم (للمقارنة)
        const oldLink = `/ar/product/${testProduct.id}`;
        console.log(`\n   🔗 الرابط القديم: ${oldLink}`);
        const oldResponse = await fetch(`${baseUrl}${oldLink}`);
        console.log(`      Status: ${oldResponse.status} ${oldResponse.ok ? '✅' : '❌'}`);
        
        console.log('\n   📊 النتيجة:');
        if (newResponse.ok) {
          console.log('   ✅ ProductCard يجب أن يعمل الآن بشكل صحيح');
          console.log(`   🎯 عند الضغط على منتج سيفتح: ${baseUrl}${newLink}`);
        } else {
          console.log('   ❌ لا يزال هناك مشكلة في الرابط');
        }
      }
    }
  } catch (error) {
    console.error('❌ خطأ في اختبار روابط ProductCard:', error.message);
  }
}

async function main() {
  console.log('🚀 بدء اختبار صفحة تفاصيل المنتج\n');
  
  await testProductDetailsPage();
  await testProductCardLinks();
  
  console.log('\n📋 ملخص الإصلاحات:');
  console.log('1. ✅ تحديث ProductCard لاستخدام /product-details/');
  console.log('2. ✅ تحديث صفحة تفاصيل المنتج لاستخدام MySQL');
  console.log('3. ✅ إصلاح أنواع البيانات (feature_text_ar, spec_key_ar)');
  console.log('4. ✅ إضافة معالجة أخطاء الصور');
  console.log('5. ✅ تحديث عرض البيانات للتوافق مع قاعدة البيانات');
  
  console.log('\n🎯 النتيجة المتوقعة:');
  console.log('- ✅ عند الضغط على منتج → تفتح صفحة تفاصيل المنتج');
  console.log('- ✅ صفحة تفاصيل المنتج تعرض جميع البيانات');
  console.log('- ✅ الصور تظهر بشكل صحيح');
  console.log('- ✅ المميزات والمواصفات تظهر');
  console.log('- ✅ أزرار إضافة للسلة وواتساب تعمل');
  
  console.log('\n🏁 انتهاء اختبار صفحة تفاصيل المنتج');
}

main();
