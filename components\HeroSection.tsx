import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { Locale } from '../lib/i18n';
import { useSiteSettings } from '../src/hooks/useSiteSettings';

interface HeroSectionProps {
  locale: Locale;
}

const HeroSection: React.FC<HeroSectionProps> = ({ locale }) => {
  const { settings, loading } = useSiteSettings();
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [whatsappNumber, setWhatsappNumber] = useState('+966501234567');

  // الصور الافتراضية في حالة عدم وجود إعدادات
  const defaultImages = [
    'https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=1200&h=800&fit=crop',
    'https://images.unsplash.com/photo-1571115764595-644a1f56a55c?w=1200&h=800&fit=crop',
    'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=1200&h=800&fit=crop'
  ];

  // استخدام الصور من الإعدادات أو الصور الافتراضية
  const heroImages = settings?.heroImages?.filter(img => img.trim() !== '') || defaultImages;

  // تغيير الصورة كل 5 ثوانٍ
  useEffect(() => {
    if (heroImages.length > 1) {
      const interval = setInterval(() => {
        setCurrentImageIndex((prevIndex) =>
          (prevIndex + 1) % heroImages.length
        );
      }, 5000);

      return () => clearInterval(interval);
    }
  }, [heroImages.length]);

  // قراءة رقم الواتساب من الإعدادات
  useEffect(() => {
    // استخدام فقط إعدادات التواصل الجديدة، تجاهل الرقم القديم
    if (settings?.communicationSettings?.whatsapp?.businessNumber) {
      setWhatsappNumber(settings.communicationSettings.whatsapp.businessNumber);
    }
  }, [settings]);

  // عرض حالة التحميل
  if (loading) {
    return (
      <section className="relative min-h-screen flex items-center justify-center overflow-hidden bg-gray-900">
        <div className="text-center text-white">
          <i className="ri-loader-4-line text-4xl animate-spin mb-4"></i>
          <p>{locale === 'ar' ? 'جاري التحميل...' : 'Loading...'}</p>
        </div>
      </section>
    );
  }

  return (
    <section className="relative min-h-screen flex items-center justify-center overflow-hidden bg-hero-pattern">
      {/* Background Images Slider */}
      <div className="absolute inset-0 z-0">
        {heroImages.map((image, index) => (
          <div
            key={index}
            className={`absolute inset-0 transition-all duration-1000 ${
              index === currentImageIndex ? 'opacity-100 scale-100' : 'opacity-0 scale-105'
            }`}
          >
            <img
              src={image}
              alt={`Hero Image ${index + 1}`}
              className="w-full h-full object-cover transition-transform duration-1000"
              onError={(e) => {
                (e.target as HTMLImageElement).src = defaultImages[0];
              }}
            />
          </div>
        ))}
        <div className="absolute inset-0 bg-gradient-to-br from-dark-900/80 via-primary-900/60 to-secondary-900/80"></div>
        <div className="absolute inset-0 bg-gradient-to-t from-black/50 via-transparent to-transparent"></div>
      </div>

      {/* Slider Indicators */}
      {heroImages.length > 1 && (
        <div className="absolute bottom-20 left-1/2 transform -translate-x-1/2 flex gap-3 z-20">
          {heroImages.map((_, index) => (
            <button
              key={index}
              onClick={() => setCurrentImageIndex(index)}
              className={`relative transition-all duration-500 ${
                index === currentImageIndex
                  ? 'w-8 h-3 bg-white rounded-full shadow-glow'
                  : 'w-3 h-3 bg-white/50 hover:bg-white/70 rounded-full'
              }`}
              aria-label={`${locale === 'ar' ? 'الانتقال للصورة' : 'Go to image'} ${index + 1}`}
            >
              {index === currentImageIndex && (
                <div className="absolute inset-0 bg-primary rounded-full animate-pulse"></div>
              )}
            </button>
          ))}
        </div>
      )}

      {/* Floating Elements */}
      <div className="absolute inset-0 z-5 pointer-events-none">
        <div className="absolute top-20 left-10 w-20 h-20 bg-primary/20 rounded-full animate-float blur-sm"></div>
        <div className="absolute top-40 right-20 w-16 h-16 bg-secondary/20 rounded-full animate-float" style={{ animationDelay: '1s' }}></div>
        <div className="absolute bottom-40 left-20 w-12 h-12 bg-accent/20 rounded-full animate-float" style={{ animationDelay: '2s' }}></div>
        <div className="absolute top-1/3 right-1/4 w-8 h-8 bg-white/10 rounded-full animate-float" style={{ animationDelay: '0.5s' }}></div>
        <div className="absolute bottom-1/3 left-1/3 w-6 h-6 bg-primary/30 rounded-full animate-float" style={{ animationDelay: '1.5s' }}></div>

        {/* Geometric shapes */}
        <div className="absolute top-1/4 left-1/4 w-4 h-4 border-2 border-white/20 rotate-45 animate-spin-slow"></div>
        <div className="absolute bottom-1/4 right-1/4 w-6 h-6 border-2 border-primary/30 rounded-full animate-pulse-slow"></div>
      </div>

      {/* Content */}
      <div className="relative z-10 text-center text-white px-4 max-w-6xl mx-auto">
        {/* Badge */}
        <div className="inline-flex items-center gap-3 glass-effect rounded-full px-8 py-3 mb-8 animate-fadeInUp shadow-glow">
          <div className="w-8 h-8 bg-gradient-primary rounded-full flex items-center justify-center animate-glow">
            <i className="ri-award-line text-white text-lg"></i>
          </div>
          <span className="text-sm font-semibold tracking-wide">
            {locale === 'ar' ? '15+ سنة من التميز والإبداع' : '15+ Years of Excellence & Innovation'}
          </span>
          <div className="w-2 h-2 bg-accent rounded-full animate-pulse"></div>
        </div>

        <h1 className="text-6xl md:text-8xl lg:text-9xl font-bold mb-8 animate-fadeInUp leading-tight">
          <span className="block hero-title text-shadow-glow">
            {locale === 'ar'
              ? 'معدات الضيافة'
              : 'Hospitality'
            }
          </span>
          <span className="block text-4xl md:text-6xl lg:text-7xl mt-2 text-white/90 font-light text-outline">
            {locale === 'ar'
              ? 'عالية الجودة'
              : 'Equipment'
            }
          </span>
        </h1>

        <p className="text-xl md:text-2xl lg:text-3xl mb-8 text-gray-200 animate-fadeInUp max-w-4xl mx-auto leading-relaxed font-light" style={{ animationDelay: '0.2s' }}>
          {locale === 'ar'
            ? 'نوفر أفضل المعدات للمطاعم والفنادق والمقاهي مع ضمان الجودة والخدمة المتميزة في جميع أنحاء المملكة'
            : 'We provide the finest equipment for restaurants, hotels, and cafes with quality assurance and exceptional service throughout the Kingdom'
          }
        </p>

        {/* Features */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-12 animate-fadeInUp max-w-4xl mx-auto" style={{ animationDelay: '0.3s' }}>
          <div className="glass-effect rounded-2xl p-6 hover:shadow-glow transition-all duration-300 group">
            <div className="w-12 h-12 bg-gradient-to-br from-green-400 to-green-600 rounded-xl flex items-center justify-center mb-4 mx-auto group-hover:scale-110 transition-transform duration-300">
              <i className="ri-shield-check-line text-white text-xl"></i>
            </div>
            <h3 className="font-bold text-lg mb-2">{locale === 'ar' ? 'ضمان 5 سنوات' : '5-Year Warranty'}</h3>
            <p className="text-sm text-gray-300">{locale === 'ar' ? 'ضمان شامل على جميع المنتجات' : 'Comprehensive warranty on all products'}</p>
          </div>
          <div className="glass-effect rounded-2xl p-6 hover:shadow-glow transition-all duration-300 group">
            <div className="w-12 h-12 bg-gradient-to-br from-blue-400 to-blue-600 rounded-xl flex items-center justify-center mb-4 mx-auto group-hover:scale-110 transition-transform duration-300">
              <i className="ri-truck-line text-white text-xl"></i>
            </div>
            <h3 className="font-bold text-lg mb-2">{locale === 'ar' ? 'توصيل مجاني' : 'Free Delivery'}</h3>
            <p className="text-sm text-gray-300">{locale === 'ar' ? 'توصيل سريع وآمن لجميع المناطق' : 'Fast and secure delivery to all regions'}</p>
          </div>
          <div className="glass-effect rounded-2xl p-6 hover:shadow-glow transition-all duration-300 group">
            <div className="w-12 h-12 bg-gradient-to-br from-purple-400 to-purple-600 rounded-xl flex items-center justify-center mb-4 mx-auto group-hover:scale-110 transition-transform duration-300">
              <i className="ri-customer-service-2-line text-white text-xl"></i>
            </div>
            <h3 className="font-bold text-lg mb-2">{locale === 'ar' ? 'دعم فني 24/7' : '24/7 Support'}</h3>
            <p className="text-sm text-gray-300">{locale === 'ar' ? 'فريق دعم متخصص على مدار الساعة' : 'Specialized support team around the clock'}</p>
          </div>
        </div>

        <div className="flex flex-col sm:flex-row gap-6 justify-center animate-fadeInUp max-w-4xl mx-auto" style={{ animationDelay: '0.4s' }}>
          <Link
            href={`/${locale}/products`}
            className="group magnetic-button btn-modern gradient-primary text-white px-10 py-5 rounded-2xl font-bold text-lg transition-all duration-500 transform hover:scale-105 hover:shadow-glow-lg flex items-center justify-center gap-3 relative overflow-hidden gpu-accelerated"
          >
            <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-700"></div>
            <div className="absolute inset-0 bg-gradient-to-r from-primary-400 to-primary-600 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
            <i className="ri-shopping-bag-line text-xl relative z-10 group-hover:animate-bounce"></i>
            <span className="relative z-10">{locale === 'ar' ? 'تصفح المنتجات' : 'Browse Products'}</span>
            <i className="ri-arrow-right-line group-hover:translate-x-2 transition-transform duration-300 relative z-10"></i>
          </Link>
          <Link
            href={`/${locale}/contact`}
            className="group magnetic-button btn-modern glass-effect border-2 border-white/30 text-white hover:bg-white/10 hover:border-white/50 px-10 py-5 rounded-2xl font-bold text-lg transition-all duration-500 transform hover:scale-105 hover:shadow-glow flex items-center justify-center gap-3 gpu-accelerated"
          >
            <i className="ri-phone-line text-xl group-hover:animate-pulse"></i>
            <span>{locale === 'ar' ? 'تواصل معنا' : 'Contact Us'}</span>
          </Link>
          <a
            href={`https://wa.me/${whatsappNumber.replace(/[^0-9]/g, '')}?text=${encodeURIComponent(locale === 'ar' ? 'مرحباً، أريد الاستفسار عن منتجاتكم' : 'Hello, I would like to inquire about your products')}`}
            target="_blank"
            rel="noopener noreferrer"
            className="group magnetic-button btn-modern bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white px-10 py-5 rounded-2xl font-bold text-lg transition-all duration-500 transform hover:scale-105 hover:shadow-glow-lg flex items-center justify-center gap-3 relative overflow-hidden gpu-accelerated"
          >
            <div className="absolute inset-0 bg-gradient-to-r from-green-400 to-green-500 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
            <i className="ri-whatsapp-line text-xl animate-bounce-slow group-hover:animate-bounce relative z-10"></i>
            <span className="relative z-10">{locale === 'ar' ? 'واتساب' : 'WhatsApp'}</span>
          </a>
        </div>
      </div>

      {/* Scroll Indicator */}
      <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 text-white animate-bounce z-20">
        <div className="flex flex-col items-center gap-3 glass-effect rounded-full px-6 py-4 hover:shadow-glow transition-all duration-300 cursor-pointer group">
          <span className="text-sm font-medium group-hover:text-primary-200 transition-colors duration-300">
            {locale === 'ar' ? 'اكتشف المزيد' : 'Discover More'}
          </span>
          <div className="w-8 h-8 border-2 border-white/50 rounded-full flex items-center justify-center group-hover:border-primary-200 transition-colors duration-300">
            <i className="ri-arrow-down-line text-lg group-hover:translate-y-1 transition-transform duration-300"></i>
          </div>
        </div>
      </div>
    </section>
  );
};

export default HeroSection;
