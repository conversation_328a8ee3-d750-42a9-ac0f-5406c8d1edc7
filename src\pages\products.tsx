import React, { useState, useMemo, useEffect } from 'react';
import Navbar from '../components/Navbar';
import Footer from '../components/Footer';
import SidebarFilters from '../components/SidebarFilters';
import ProductCard from '../components/ProductCard';
import { useTranslation } from 'next-i18next';
import { serverSideTranslations } from 'next-i18next/serverSideTranslations';
import { GetStaticProps } from 'next';
import { Product } from '../types/database';
import { productsApi } from '../lib/api';

const ProductsPage = () => {
  const { t } = useTranslation('common');
  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [selectedSubcategory, setSelectedSubcategory] = useState('all');
  const [priceRange, setPriceRange] = useState('all');
  const [availability, setAvailability] = useState<string[]>(['in-stock', 'out-stock']);
  const [sortBy, setSortBy] = useState('newest');

  useEffect(() => {
    const fetchProducts = async () => {
      try {
        const productsData = await productsApi.getAll();
        setProducts(productsData);
      } catch (error) {
        console.error('Error fetching products:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchProducts();
  }, []);

  // فلترة المنتجات
  const filteredProducts = useMemo(() => {
    let filtered = products.filter(product => {
      // فلتر البحث
      const matchesSearch = product.titleAr.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           product.descriptionAr.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           product.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           product.description.toLowerCase().includes(searchTerm.toLowerCase());

      // فلتر الفئة
      const matchesCategory = selectedCategory === 'all' || product.categoryId === selectedCategory;

      // فلتر الفئة الفرعية
      const matchesSubcategory = selectedSubcategory === 'all' || product.subcategoryId === selectedSubcategory;

      // فلتر السعر
      let matchesPrice = true;
      if (priceRange === 'low') matchesPrice = product.price < 1000;
      else if (priceRange === 'medium') matchesPrice = product.price >= 1000 && product.price <= 5000;
      else if (priceRange === 'high') matchesPrice = product.price > 5000;

      // فلتر التوفر
      const matchesAvailability = availability.includes(product.available ? 'in-stock' : 'out-stock');

      return matchesSearch && matchesCategory && matchesSubcategory && matchesPrice && matchesAvailability;
    });

    // ترتيب المنتجات
    switch (sortBy) {
      case 'price-low':
        filtered.sort((a, b) => a.price - b.price);
        break;
      case 'price-high':
        filtered.sort((a, b) => b.price - a.price);
        break;
      case 'name':
        filtered.sort((a, b) => a.titleAr.localeCompare(b.titleAr, 'ar'));
        break;
      default:
        // الأحدث (حسب الترتيب الافتراضي)
        filtered.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
        break;
    }

    return filtered;
  }, [products, searchTerm, selectedCategory, selectedSubcategory, priceRange, availability, sortBy]);
  return (
    <>
      <Navbar />
      <main>
        {/* Page Header */}
        <section className="bg-primary py-12">
          <div className="container mx-auto px-4">
            <h1 className="text-3xl md:text-4xl font-bold text-white text-center">
              {t('products.title')}
            </h1>
            <p className="text-white/80 text-center mt-4 max-w-2xl mx-auto">
              {t('products.subtitle')}
            </p>
          </div>
        </section>
        {/* Products Section */}
        <section className="py-12">
          <div className="container mx-auto px-4">
            <div className="flex flex-col lg:flex-row gap-8">
              {/* Filters Sidebar */}
              <div className="lg:w-1/4">
                <SidebarFilters
                  selectedCategory={selectedCategory}
                  setSelectedCategory={setSelectedCategory}
                  selectedSubcategory={selectedSubcategory}
                  setSelectedSubcategory={setSelectedSubcategory}
                  priceRange={priceRange}
                  setPriceRange={setPriceRange}
                  availability={availability}
                  setAvailability={setAvailability}
                />
              </div>
              {/* Products Grid */}
              <div className="lg:w-3/4">
                {/* Search and Sort */}
                <div className="bg-white rounded-lg shadow-md p-4 mb-8">
                  <div className="flex flex-col md:flex-row gap-4 items-center justify-between">
                    <div className="relative flex-1">
                      <input
                        type="text"
                        placeholder={t('products.searchPlaceholder')}
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        className="w-full pr-10 py-2 border-none bg-gray-50 rounded-lg focus:ring-2 focus:ring-primary/20 text-sm"
                      />
                      <div className="absolute left-3 top-1/2 -translate-y-1/2 w-5 h-5 flex items-center justify-center text-gray-400">
                        <i className="ri-search-line"></i>
                      </div>
                    </div>
                    <div className="flex items-center gap-4">
                      <label className="text-gray-700 whitespace-nowrap">{t('products.sortBy')}</label>
                      <select
                        value={sortBy}
                        onChange={(e) => setSortBy(e.target.value)}
                        className="pr-4 py-2 border-none bg-gray-50 rounded-lg focus:ring-2 focus:ring-primary/20 text-sm"
                      >
                        <option value="newest">{t('products.newest')}</option>
                        <option value="price-low">{t('products.priceLowToHigh')}</option>
                        <option value="price-high">{t('products.priceHighToLow')}</option>
                        <option value="name">{t('products.nameAZ')}</option>
                      </select>
                    </div>
                  </div>
                </div>

                {/* عدد النتائج */}
                {!loading && (
                  <div className="mb-6">
                    <p className="text-gray-600">
                      {t('products.showing')} {filteredProducts.length} {t('products.of')} {products.length} {t('products.product')}
                    </p>
                  </div>
                )}

                {/* Products Grid */}
                {loading ? (
                  <div className="text-center py-16">
                    <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
                    <p className="text-gray-600">جاري تحميل المنتجات...</p>
                  </div>
                ) : filteredProducts.length > 0 ? (
                  <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-8">
                    {filteredProducts.map((product) => (
                      <ProductCard
                        key={product.id}
                        id={parseInt(product.id)}
                        image={product.images[0]}
                        title={product.titleAr}
                        description={product.descriptionAr}
                        price={product.price}
                        available={product.available}
                      />
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-16">
                    <i className="ri-search-line text-6xl text-gray-400 mb-4"></i>
                    <h3 className="text-xl font-bold text-gray-800 mb-2">{t('products.noProducts')}</h3>
                    <p className="text-gray-600">{t('products.noProductsMessage')}</p>
                  </div>
                )}
              </div>
            </div>
          </div>
        </section>
      </main>
      <Footer />
    </>
  );
};

export const getStaticProps: GetStaticProps = async ({ locale }) => {
  return {
    props: {
      ...(await serverSideTranslations(locale ?? 'ar', ['common'])),
    },
  };
};

export default ProductsPage;
