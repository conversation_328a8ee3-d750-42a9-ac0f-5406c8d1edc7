import fs from 'fs';
import path from 'path';
import { Database, Category, Subcategory, Product, SiteSettings } from '../types/database';
import { readEncryptedDatabase, writeEncryptedDatabase } from './secure-database';

// مسار قاعدة البيانات القديمة (للتوافق المؤقت)
const DATABASE_PATH = path.join(process.cwd(), 'src/data/database.json');

// قراءة قاعدة البيانات (الآن من النظام المشفر)
export function readDatabase(): Database {
  try {
    return readEncryptedDatabase();
  } catch (error) {
    console.error('Error reading encrypted database:', error);

    // محاولة قراءة الملف القديم كنسخة احتياطية
    if (fs.existsSync(DATABASE_PATH)) {
      console.log('Falling back to original database file...');
      const fileContents = fs.readFileSync(DATABASE_PATH, 'utf8');
      return JSON.parse(fileContents);
    }

    throw new Error('Failed to read database');
  }
}

// كتابة قاعدة البيانات (الآن إلى النظام المشفر)
export function writeDatabase(data: Database): void {
  try {
    writeEncryptedDatabase(data);
  } catch (error) {
    console.error('Error writing encrypted database:', error);
    throw new Error('Failed to write database');
  }
}

// الحصول على جميع الفئات
export function getCategories(): Category[] {
  const db = readDatabase();
  return db.categories.filter(category => category.isActive);
}

// الحصول على فئة بواسطة ID
export function getCategoryById(id: string): Category | null {
  const db = readDatabase();
  return db.categories.find(category => category.id === id) || null;
}

// الحصول على جميع الفئات الفرعية
export function getSubcategories(): Subcategory[] {
  const db = readDatabase();
  return db.subcategories.filter(subcategory => subcategory.isActive);
}

// الحصول على الفئات الفرعية بواسطة فئة
export function getSubcategoriesByCategory(categoryId: string): Subcategory[] {
  const db = readDatabase();
  return db.subcategories.filter(
    subcategory => subcategory.categoryId === categoryId && subcategory.isActive
  );
}

// الحصول على فئة فرعية بواسطة ID
export function getSubcategoryById(id: string): Subcategory | null {
  const db = readDatabase();
  return db.subcategories.find(subcategory => subcategory.id === id) || null;
}

// الحصول على جميع المنتجات
export function getProducts(): Product[] {
  const db = readDatabase();
  return db.products.filter(product => product.isActive);
}

// الحصول على المنتجات المميزة
export function getFeaturedProducts(): Product[] {
  const db = readDatabase();
  return db.products.filter(product => product.isActive && product.isFeatured);
}

// الحصول على المنتجات بواسطة الفئة
export function getProductsByCategory(categoryId: string): Product[] {
  const db = readDatabase();
  return db.products.filter(
    product => product.categoryId === categoryId && product.isActive
  );
}

// الحصول على المنتجات بواسطة الفئة الفرعية
export function getProductsBySubcategory(subcategoryId: string): Product[] {
  const db = readDatabase();
  return db.products.filter(
    product => product.subcategoryId === subcategoryId && product.isActive
  );
}

// الحصول على منتج بواسطة ID
export function getProductById(id: string): Product | null {
  const db = readDatabase();
  return db.products.find(product => product.id === id) || null;
}

// البحث في المنتجات
export function searchProducts(query: string): Product[] {
  const db = readDatabase();
  const searchTerm = query.toLowerCase();
  
  return db.products.filter(product => 
    product.isActive && (
      product.title.toLowerCase().includes(searchTerm) ||
      product.titleAr.toLowerCase().includes(searchTerm) ||
      product.description.toLowerCase().includes(searchTerm) ||
      product.descriptionAr.toLowerCase().includes(searchTerm)
    )
  );
}

// الحصول على إعدادات الموقع
export function getSiteSettings(): SiteSettings {
  const db = readDatabase();
  return db.settings;
}

// تحديث إعدادات الموقع
export function updateSiteSettings(settings: Partial<SiteSettings>): void {
  const db = readDatabase();
  db.settings = { ...db.settings, ...settings };
  writeDatabase(db);
}

// إضافة فئة جديدة
export function addCategory(category: Omit<Category, 'createdAt' | 'updatedAt'>): Category {
  const db = readDatabase();
  const newCategory: Category = {
    ...category,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  };
  
  db.categories.push(newCategory);
  writeDatabase(db);
  return newCategory;
}

// تحديث فئة
export function updateCategory(id: string, updates: Partial<Category>): Category | null {
  const db = readDatabase();
  const categoryIndex = db.categories.findIndex(cat => cat.id === id);
  
  if (categoryIndex === -1) return null;
  
  db.categories[categoryIndex] = {
    ...db.categories[categoryIndex],
    ...updates,
    updatedAt: new Date().toISOString()
  };
  
  writeDatabase(db);
  return db.categories[categoryIndex];
}

// حذف فئة (إلغاء تفعيل)
export function deleteCategory(id: string): boolean {
  const db = readDatabase();
  const categoryIndex = db.categories.findIndex(cat => cat.id === id);
  
  if (categoryIndex === -1) return false;
  
  db.categories[categoryIndex].isActive = false;
  db.categories[categoryIndex].updatedAt = new Date().toISOString();
  
  writeDatabase(db);
  return true;
}

// إضافة فئة فرعية جديدة
export function addSubcategory(subcategory: Omit<Subcategory, 'createdAt' | 'updatedAt'>): Subcategory {
  const db = readDatabase();
  const newSubcategory: Subcategory = {
    ...subcategory,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  };
  
  db.subcategories.push(newSubcategory);
  writeDatabase(db);
  return newSubcategory;
}

// تحديث فئة فرعية
export function updateSubcategory(id: string, updates: Partial<Subcategory>): Subcategory | null {
  const db = readDatabase();
  const subcategoryIndex = db.subcategories.findIndex(sub => sub.id === id);
  
  if (subcategoryIndex === -1) return null;
  
  db.subcategories[subcategoryIndex] = {
    ...db.subcategories[subcategoryIndex],
    ...updates,
    updatedAt: new Date().toISOString()
  };
  
  writeDatabase(db);
  return db.subcategories[subcategoryIndex];
}

// حذف فئة فرعية (إلغاء تفعيل)
export function deleteSubcategory(id: string): boolean {
  const db = readDatabase();
  const subcategoryIndex = db.subcategories.findIndex(sub => sub.id === id);
  
  if (subcategoryIndex === -1) return false;
  
  db.subcategories[subcategoryIndex].isActive = false;
  db.subcategories[subcategoryIndex].updatedAt = new Date().toISOString();
  
  writeDatabase(db);
  return true;
}

// إضافة منتج جديد
export function addProduct(product: Omit<Product, 'id' | 'createdAt' | 'updatedAt'>): Product {
  const db = readDatabase();
  const newProduct: Product = {
    ...product,
    id: Date.now().toString(), // إنشاء ID فريد
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  };

  db.products.push(newProduct);
  writeDatabase(db);
  return newProduct;
}

// تحديث منتج
export function updateProduct(id: string, updates: Partial<Product>): Product | null {
  const db = readDatabase();
  const productIndex = db.products.findIndex(prod => prod.id === id);
  
  if (productIndex === -1) return null;
  
  db.products[productIndex] = {
    ...db.products[productIndex],
    ...updates,
    updatedAt: new Date().toISOString()
  };
  
  writeDatabase(db);
  return db.products[productIndex];
}

// حذف منتج (إلغاء تفعيل)
export function deleteProduct(id: string): boolean {
  const db = readDatabase();
  const productIndex = db.products.findIndex(prod => prod.id === id);
  
  if (productIndex === -1) return false;
  
  db.products[productIndex].isActive = false;
  db.products[productIndex].updatedAt = new Date().toISOString();
  
  writeDatabase(db);
  return true;
}
