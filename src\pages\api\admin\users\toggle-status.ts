import { NextApiRequest, NextApiResponse } from 'next';
import jwt from 'jsonwebtoken';
import { executeQuery, executeQuerySingle } from '../../../../lib/database-config';

const JWT_SECRET = process.env.JWT_SECRET || 'your-super-secret-jwt-key-change-this-in-production';

interface AdminUser {
  id: number;
  username: string;
  is_active: boolean;
}

// التحقق من صحة JWT token
function verifyToken(token: string) {
  try {
    return jwt.verify(token, JWT_SECRET) as any;
  } catch (error) {
    return null;
  }
}

// استخراج token من الطلب
function extractToken(req: NextApiRequest): string | null {
  const authHeader = req.headers.authorization;
  if (authHeader && authHeader.startsWith('Bearer ')) {
    return authHeader.substring(7);
  }
  
  const tokenFromCookie = req.cookies.authToken;
  if (tokenFromCookie) {
    return tokenFromCookie;
  }
  
  return null;
}

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'PUT') {
    return res.status(405).json({ message: 'Method not allowed' });
  }

  try {
    // التحقق من المصادقة
    const token = extractToken(req);
    if (!token) {
      return res.status(401).json({ 
        message: 'غير مصرح لك بالوصول',
        success: false 
      });
    }

    const decoded = verifyToken(token);
    if (!decoded) {
      return res.status(401).json({ 
        message: 'رمز المصادقة غير صحيح',
        success: false 
      });
    }

    const { userId } = req.body;

    // التحقق من وجود معرف المستخدم
    if (!userId) {
      return res.status(400).json({ 
        message: 'معرف المستخدم مطلوب',
        success: false 
      });
    }

    // التحقق من أن المستخدم لا يعطل نفسه
    if (decoded.userId === userId) {
      return res.status(400).json({ 
        message: 'لا يمكنك تعطيل حسابك الخاص',
        success: false 
      });
    }

    // الحصول على حالة المستخدم الحالية
    const user = await executeQuerySingle<AdminUser>(
      'SELECT id, username, is_active FROM admins WHERE id = ? AND deleted_at IS NULL',
      [userId]
    );

    if (!user) {
      return res.status(404).json({ 
        message: 'المستخدم غير موجود',
        success: false 
      });
    }

    // تبديل الحالة
    const newStatus = !user.is_active;
    
    await executeQuery(
      'UPDATE admins SET is_active = ?, updated_at = NOW() WHERE id = ?',
      [newStatus, userId]
    );

    return res.status(200).json({
      success: true,
      message: newStatus ? 'تم تفعيل المستخدم بنجاح' : 'تم إلغاء تفعيل المستخدم بنجاح',
      newStatus
    });

  } catch (error) {
    console.error('Toggle user status error:', error);
    return res.status(500).json({ 
      message: 'حدث خطأ في الخادم',
      success: false 
    });
  }
}
