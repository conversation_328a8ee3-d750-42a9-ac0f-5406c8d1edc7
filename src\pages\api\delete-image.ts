import { NextApiRequest, NextApiResponse } from 'next';
import fs from 'fs';
import path from 'path';
import jwt from 'jsonwebtoken';

const JWT_SECRET = process.env.JWT_SECRET || 'your-super-secret-jwt-key-change-this-in-production';

// التحقق من صحة JWT token
function verifyToken(token: string) {
  try {
    return jwt.verify(token, JWT_SECRET) as any;
  } catch (error) {
    return null;
  }
}

// استخراج token من الطلب
function extractToken(req: NextApiRequest): string | null {
  const authHeader = req.headers.authorization;
  if (authHeader && authHeader.startsWith('Bearer ')) {
    return authHeader.substring(7);
  }
  
  const tokenFromCookie = req.cookies.authToken;
  if (tokenFromCookie) {
    return tokenFromCookie;
  }
  
  return null;
}

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'DELETE') {
    return res.status(405).json({
      success: false,
      message: 'Method not allowed',
      messageAr: 'الطريقة غير مسموحة'
    });
  }

  // التحقق من المصادقة
  const token = extractToken(req);
  if (!token) {
    return res.status(401).json({
      success: false,
      message: 'Authentication required',
      messageAr: 'المصادقة مطلوبة'
    });
  }

  const decoded = verifyToken(token);
  if (!decoded) {
    return res.status(401).json({
      success: false,
      message: 'Invalid token',
      messageAr: 'رمز المصادقة غير صحيح'
    });
  }

  try {
    const { imageUrl } = req.body;

    if (!imageUrl) {
      return res.status(400).json({
        success: false,
        message: 'Image URL is required',
        messageAr: 'رابط الصورة مطلوب'
      });
    }

    // التحقق من أن الصورة في مجلد uploads
    if (!imageUrl.startsWith('/uploads/')) {
      return res.status(400).json({
        success: false,
        message: 'Invalid image URL',
        messageAr: 'رابط الصورة غير صحيح'
      });
    }

    // استخراج اسم الملف من الرابط
    const fileName = imageUrl.replace('/uploads/', '');
    const filePath = path.join(process.cwd(), 'public', 'uploads', fileName);

    // التحقق من وجود الملف
    if (!fs.existsSync(filePath)) {
      return res.status(404).json({
        success: false,
        message: 'Image file not found',
        messageAr: 'ملف الصورة غير موجود'
      });
    }

    // حذف الملف
    fs.unlinkSync(filePath);

    console.log('🗑️ تم حذف صورة الهيرو:', fileName);

    res.status(200).json({
      success: true,
      message: 'Image deleted successfully',
      messageAr: 'تم حذف الصورة بنجاح'
    });

  } catch (error) {
    console.error('❌ خطأ في حذف الصورة:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete image',
      messageAr: 'فشل في حذف الصورة',
      error: process.env.NODE_ENV === 'development' ? (error as Error).message : undefined
    });
  }
}
