import React from 'react';
import Link from 'next/link';
import { useTranslation } from 'next-i18next';

const HeroSlider = () => {
  const { t } = useTranslation('common');
  const [current, setCurrent] = React.useState(0);
  const touchStartX = React.useRef<number | null>(null);
  const touchEndX = React.useRef<number | null>(null);
  const sliderRef = React.useRef<HTMLDivElement>(null);

  const slides = [
    {
      image: 'https://readdy.ai/api/search-image?query=luxury%20hotel%20restaurant%20equipment%2C%20elegant%20dining%20tables%20with%20white%20tablecloths%2C%20fine%20dining%20setup%2C%20professional%20kitchen%20equipment%2C%20high-end%20hospitality%20industry%2C%20soft%20lighting%2C%20modern%20interior%20design%2C%205-star%20hotel%20atmosphere&width=1600&height=800&seq=1&orientation=landscape',
      alt: t('hero.slides.slide1.alt'),
      title: t('hero.slides.slide1.title'),
      desc: t('hero.slides.slide1.description'),
    },
    {
      image: 'https://readdy.ai/api/search-image?query=professional%20restaurant%20kitchen%20equipment%2C%20stainless%20steel%20appliances%2C%20chef%20working%20in%20modern%20kitchen%2C%20commercial%20cooking%20equipment%2C%20hotel%20supplies%2C%20clean%20and%20organized%20workspace%2C%20hospitality%20industry&width=1600&height=800&seq=2&orientation=landscape',
      alt: t('hero.slides.slide2.alt'),
      title: t('hero.slides.slide2.title'),
      desc: t('hero.slides.slide2.description'),
    },
    {
      image: 'https://readdy.ai/api/search-image?query=elegant%20hotel%20room%20interior%2C%20luxury%20bedding%2C%20premium%20hotel%20furniture%2C%20sophisticated%20design%2C%20high-quality%20hotel%20amenities%2C%20comfortable%20accommodation%2C%20hospitality%20industry%2C%205-star%20hotel%20room&width=1600&height=800&seq=3&orientation=landscape',
      alt: t('hero.slides.slide3.alt'),
      title: t('hero.slides.slide3.title'),
      desc: t('hero.slides.slide3.description'),
    },
  ];

  const slidesCount = slides.length;

  // Autoplay
  React.useEffect(() => {
    const interval = setInterval(() => {
      setCurrent((prev) => (prev + 1) % slidesCount);
    }, 5000);
    return () => clearInterval(interval);
  }, [slidesCount]);

  // Swipe/drag handlers
  const handleTouchStart = (e: React.TouchEvent) => {
    touchStartX.current = e.touches[0].clientX;
  };
  const handleTouchMove = (e: React.TouchEvent) => {
    touchEndX.current = e.touches[0].clientX;
  };
  const handleTouchEnd = () => {
    if (touchStartX.current !== null && touchEndX.current !== null) {
      const diff = touchStartX.current - touchEndX.current;
      if (diff > 50) {
        goToNext();
      } else if (diff < -50) {
        goToPrev();
      }
    }
    touchStartX.current = null;
    touchEndX.current = null;
  };
  // Mouse drag for desktop
  const mouseDown = React.useRef(false);
  const mouseStartX = React.useRef<number | null>(null);
  const mouseEndX = React.useRef<number | null>(null);
  const handleMouseDown = (e: React.MouseEvent) => {
    mouseDown.current = true;
    mouseStartX.current = e.clientX;
  };
  const handleMouseMove = (e: React.MouseEvent) => {
    if (!mouseDown.current) return;
    mouseEndX.current = e.clientX;
  };
  const handleMouseUp = () => {
    if (mouseDown.current && mouseStartX.current !== null && mouseEndX.current !== null) {
      const diff = mouseStartX.current - mouseEndX.current;
      if (diff > 50) {
        goToNext();
      } else if (diff < -50) {
        goToPrev();
      }
    }
    mouseDown.current = false;
    mouseStartX.current = null;
    mouseEndX.current = null;
  };
  // Keyboard accessibility
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'ArrowLeft') goToPrev();
    if (e.key === 'ArrowRight') goToNext();
  };
  // Navigation helpers
  const goToNext = () => setCurrent((prev) => (prev + 1) % slidesCount);
  const goToPrev = () => setCurrent((prev) => (prev - 1 + slidesCount) % slidesCount);

  return (
    <section
      className="slider-container h-[500px] md:h-[600px] relative"
      tabIndex={0}
      aria-roledescription="carousel"
      aria-label={t('hero.navigation.carousel')}
      onKeyDown={handleKeyDown}
    >
      <div
        ref={sliderRef}
        className="slider h-full"
        style={{ transform: `translateX(-${current * 100}%)`, display: 'flex', transition: 'transform 0.5s ease-in-out' }}
        onTouchStart={handleTouchStart}
        onTouchMove={handleTouchMove}
        onTouchEnd={handleTouchEnd}
        onMouseDown={handleMouseDown}
        onMouseMove={handleMouseMove}
        onMouseUp={handleMouseUp}
        onMouseLeave={handleMouseUp}
        role="group"
        aria-live="polite"
      >
        {slides.map((slide, idx) => (
          <div
            className="slide relative min-w-full"
            key={idx}
            aria-hidden={current !== idx}
            tabIndex={current === idx ? 0 : -1}
            role="group"
            aria-roledescription="slide"
            aria-label={`${t('hero.navigation.slideLabel')} ${idx + 1} ${t('hero.navigation.of')} ${slidesCount}`}
          >
            <div className="absolute inset-0 bg-gradient-to-r from-primary/80 to-transparent"></div>
            <img src={slide.image} alt={slide.alt} className="w-full h-full object-cover object-top" />
            <div className="absolute inset-0 flex items-center">
              <div className="container mx-auto px-4">
                <div className="max-w-xl">
                  <h1 className="text-4xl md:text-5xl font-bold text-white mb-4">{slide.title}</h1>
                  <p className="text-white text-lg mb-8">{slide.desc}</p>
                  <div className="flex gap-4">
                    <Link href="/products" className="bg-white text-primary px-6 py-3 rounded-button font-medium hover:bg-gray-100 transition-colors whitespace-nowrap">{t('hero.cta')}</Link>
                    <Link href="/contact" className="bg-primary text-white px-6 py-3 rounded-button font-medium border border-white hover:bg-primary/90 transition-colors whitespace-nowrap">{t('hero.contact')}</Link>
                  </div>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>
      {/* Navigation Buttons */}
      <button
        className="absolute top-1/2 right-4 -translate-y-1/2 bg-white/80 hover:bg-white text-primary rounded-full w-10 h-10 flex items-center justify-center shadow z-20"
        onClick={goToNext}
        aria-label={t('hero.navigation.nextSlide')}
        tabIndex={0}
        style={{outline: 'none'}}
      >
        <i className="ri-arrow-left-s-line text-2xl" aria-hidden="true"></i>
      </button>
      <button
        className="absolute top-1/2 left-4 -translate-y-1/2 bg-white/80 hover:bg-white text-primary rounded-full w-10 h-10 flex items-center justify-center shadow z-20"
        onClick={goToPrev}
        aria-label={t('hero.navigation.prevSlide')}
        tabIndex={0}
        style={{outline: 'none'}}
      >
        <i className="ri-arrow-right-s-line text-2xl" aria-hidden="true"></i>
      </button>
      <div className="slider-nav absolute bottom-6 left-1/2 -translate-x-1/2 flex gap-2 z-10">
        {slides.map((_, idx) => (
          <button
            key={idx}
            className={`slider-dot w-3 h-3 rounded-full ${current === idx ? 'bg-white' : 'bg-white/50'}`}
            onClick={() => setCurrent(idx)}
            aria-label={`${t('hero.navigation.goToSlide')} ${idx + 1}`}
            aria-current={current === idx ? 'true' : undefined}
            tabIndex={0}
          />
        ))}
      </div>
    </section>
  );
};

export default HeroSlider;
