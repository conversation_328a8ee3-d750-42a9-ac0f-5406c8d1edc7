import React, { useState } from 'react';
import Head from 'next/head';
import AdminLayout from '../../components/admin/AdminLayout';

const TestDatabase = () => {
  const [testResult, setTestResult] = useState(null);
  const [statsResult, setStatsResult] = useState(null);
  const [loading, setLoading] = useState(false);
  const [statsLoading, setStatsLoading] = useState(false);

  const runTest = async () => {
    setLoading(true);
    setTestResult(null);

    try {
      const response = await fetch('/api/test-db-connection');
      const data = await response.json();
      setTestResult(data);
    } catch (error) {
      setTestResult({
        success: false,
        message: 'Network error',
        messageAr: 'خطأ في الشبكة',
        error: { message: error.message }
      });
    } finally {
      setLoading(false);
    }
  };

  const runStatsTest = async () => {
    setStatsLoading(true);
    setStatsResult(null);

    try {
      const response = await fetch('/api/test-stats');
      const data = await response.json();
      setStatsResult(data);
    } catch (error) {
      setStatsResult({
        success: false,
        message: 'Network error',
        messageAr: 'خطأ في الشبكة',
        error: { message: error.message }
      });
    } finally {
      setStatsLoading(false);
    }
  };

  return (
    <>
      <Head>
        <title>اختبار قاعدة البيانات - VidMeet</title>
        <meta name="description" content="اختبار الاتصال بقاعدة البيانات" />
      </Head>

      <AdminLayout title="اختبار قاعدة البيانات">
        <div className="space-y-6">
          {/* Header */}
          <div className="bg-white rounded-2xl p-6 shadow-[0_0_50px_rgba(0,0,0,0.05)] border border-[#E2E8F0]">
            <h1 className="text-2xl font-bold text-[#1E293B] mb-4">اختبار الاتصال بقاعدة البيانات</h1>
            <p className="text-[#64748B] mb-6">
              هذه الصفحة تساعد في تشخيص مشاكل الاتصال بقاعدة البيانات والتحقق من وجود الجداول المطلوبة.
            </p>
            
            <div className="flex gap-4">
              <button
                onClick={runTest}
                disabled={loading}
                className="bg-primary text-white px-6 py-3 rounded-lg hover:bg-primary/90 disabled:opacity-50 transition-colors flex items-center gap-2"
              >
                {loading ? (
                  <>
                    <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                    جاري الاختبار...
                  </>
                ) : (
                  <>
                    <i className="ri-database-line text-lg"></i>
                    اختبار الاتصال
                  </>
                )}
              </button>

              <button
                onClick={runStatsTest}
                disabled={statsLoading}
                className="bg-green-600 text-white px-6 py-3 rounded-lg hover:bg-green-700 disabled:opacity-50 transition-colors flex items-center gap-2"
              >
                {statsLoading ? (
                  <>
                    <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                    جاري اختبار الإحصائيات...
                  </>
                ) : (
                  <>
                    <i className="ri-bar-chart-line text-lg"></i>
                    اختبار الإحصائيات
                  </>
                )}
              </button>
            </div>
          </div>

          {/* Test Results */}
          {testResult && (
            <div className={`rounded-2xl p-6 shadow-[0_0_50px_rgba(0,0,0,0.05)] border ${
              testResult.success 
                ? 'bg-green-50 border-green-200' 
                : 'bg-red-50 border-red-200'
            }`}>
              <div className="flex items-center mb-4">
                <i className={`${
                  testResult.success ? 'ri-check-line text-green-600' : 'ri-error-warning-line text-red-600'
                } text-2xl ml-3`}></i>
                <h2 className={`text-xl font-bold ${
                  testResult.success ? 'text-green-800' : 'text-red-800'
                }`}>
                  {testResult.success ? 'نجح الاختبار' : 'فشل الاختبار'}
                </h2>
              </div>

              <p className={`mb-4 ${
                testResult.success ? 'text-green-700' : 'text-red-700'
              }`}>
                {testResult.messageAr || testResult.message}
              </p>

              {/* Configuration */}
              {testResult.data?.config && (
                <div className="bg-white rounded-lg p-4 mb-4">
                  <h3 className="font-bold text-gray-800 mb-2">إعدادات الاتصال:</h3>
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="font-medium">الخادم:</span> {testResult.data.config.host}
                    </div>
                    <div>
                      <span className="font-medium">المستخدم:</span> {testResult.data.config.user}
                    </div>
                    <div>
                      <span className="font-medium">قاعدة البيانات:</span> {testResult.data.config.database}
                    </div>
                    <div>
                      <span className="font-medium">كلمة المرور:</span> {testResult.data.config.hasPassword ? 'موجودة' : 'غير موجودة'}
                    </div>
                  </div>
                </div>
              )}

              {/* Tables Test */}
              {testResult.data?.tests && (
                <div className="bg-white rounded-lg p-4 mb-4">
                  <h3 className="font-bold text-gray-800 mb-2">اختبار الجداول:</h3>
                  <div className="space-y-2">
                    {Object.entries(testResult.data.tests).map(([tableName, test]) => (
                      <div key={tableName} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                        <span className="font-medium">{tableName}:</span>
                        <div className="flex items-center gap-2">
                          {test.exists ? (
                            <>
                              <span className="text-green-600">✅ موجود</span>
                              <span className="text-sm text-gray-600">({test.count} سجل)</span>
                            </>
                          ) : (
                            <span className="text-red-600">❌ غير موجود</span>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Available Tables */}
              {testResult.data?.tables && testResult.data.tables.length > 0 && (
                <div className="bg-white rounded-lg p-4 mb-4">
                  <h3 className="font-bold text-gray-800 mb-2">الجداول الموجودة:</h3>
                  <div className="grid grid-cols-3 gap-2">
                    {testResult.data.tables.map((table, index) => (
                      <div key={index} className="bg-gray-50 p-2 rounded text-sm">
                        {table.TABLE_NAME}
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Error Details */}
              {!testResult.success && testResult.error && (
                <div className="bg-white rounded-lg p-4">
                  <h3 className="font-bold text-red-800 mb-2">تفاصيل الخطأ:</h3>
                  <div className="text-sm text-red-700 space-y-1">
                    <div><span className="font-medium">الرسالة:</span> {testResult.error.message}</div>
                    {testResult.error.code && (
                      <div><span className="font-medium">الكود:</span> {testResult.error.code}</div>
                    )}
                    {testResult.error.errno && (
                      <div><span className="font-medium">رقم الخطأ:</span> {testResult.error.errno}</div>
                    )}
                  </div>
                </div>
              )}
            </div>
          )}

          {/* Stats Test Results */}
          {statsResult && (
            <div className={`rounded-2xl p-6 shadow-[0_0_50px_rgba(0,0,0,0.05)] border ${
              statsResult.success
                ? 'bg-green-50 border-green-200'
                : 'bg-red-50 border-red-200'
            }`}>
              <div className="flex items-center mb-4">
                <i className={`${
                  statsResult.success ? 'ri-check-line text-green-600' : 'ri-error-warning-line text-red-600'
                } text-2xl ml-3`}></i>
                <h2 className={`text-xl font-bold ${
                  statsResult.success ? 'text-green-800' : 'text-red-800'
                }`}>
                  {statsResult.success ? 'نجح اختبار الإحصائيات' : 'فشل اختبار الإحصائيات'}
                </h2>
              </div>

              <p className={`mb-4 ${
                statsResult.success ? 'text-green-700' : 'text-red-700'
              }`}>
                {statsResult.messageAr || statsResult.message}
              </p>

              {/* Stats Results */}
              {statsResult.success && statsResult.stats && (
                <div className="bg-white rounded-lg p-4 mb-4">
                  <h3 className="font-bold text-gray-800 mb-4">الإحصائيات المجلبة:</h3>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <div className="bg-blue-50 p-4 rounded-lg text-center">
                      <div className="text-2xl font-bold text-blue-600">{statsResult.stats.products.total}</div>
                      <div className="text-sm text-blue-800">إجمالي المنتجات</div>
                      <div className="text-xs text-blue-600 mt-1">
                        {statsResult.stats.products.active} نشط، {statsResult.stats.products.featured} مميز
                      </div>
                    </div>

                    <div className="bg-green-50 p-4 rounded-lg text-center">
                      <div className="text-2xl font-bold text-green-600">{statsResult.stats.categories.total}</div>
                      <div className="text-sm text-green-800">إجمالي الفئات</div>
                      <div className="text-xs text-green-600 mt-1">
                        {statsResult.stats.categories.active} نشط
                      </div>
                    </div>

                    <div className="bg-purple-50 p-4 rounded-lg text-center">
                      <div className="text-2xl font-bold text-purple-600">{statsResult.stats.subcategories.total}</div>
                      <div className="text-sm text-purple-800">الفئات الفرعية</div>
                      <div className="text-xs text-purple-600 mt-1">
                        {statsResult.stats.subcategories.active} نشط
                      </div>
                    </div>

                    <div className="bg-indigo-50 p-4 rounded-lg text-center">
                      <div className="text-2xl font-bold text-indigo-600">{statsResult.stats.admins.total}</div>
                      <div className="text-sm text-indigo-800">المديرين</div>
                    </div>
                  </div>
                </div>
              )}

              {/* Error Details */}
              {!statsResult.success && statsResult.error && (
                <div className="bg-white rounded-lg p-4">
                  <h3 className="font-bold text-red-800 mb-2">تفاصيل الخطأ:</h3>
                  <div className="text-sm text-red-700 space-y-1">
                    <div><span className="font-medium">الرسالة:</span> {statsResult.error.message}</div>
                    {statsResult.error.code && (
                      <div><span className="font-medium">الكود:</span> {statsResult.error.code}</div>
                    )}
                  </div>
                </div>
              )}
            </div>
          )}

          {/* Instructions */}
          <div className="bg-blue-50 border border-blue-200 rounded-2xl p-6">
            <h3 className="text-blue-800 font-bold mb-2">تعليمات استكشاف الأخطاء:</h3>
            <ul className="text-blue-700 text-sm space-y-1">
              <li>• تأكد من تشغيل خادم MySQL</li>
              <li>• تحقق من إعدادات قاعدة البيانات في ملف .env.local</li>
              <li>• تأكد من وجود قاعدة البيانات والجداول المطلوبة</li>
              <li>• تحقق من صلاحيات المستخدم للوصول لقاعدة البيانات</li>
            </ul>
          </div>
        </div>
      </AdminLayout>
    </>
  );
};

export default TestDatabase;
