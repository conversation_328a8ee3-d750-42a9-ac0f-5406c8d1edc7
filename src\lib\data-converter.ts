// دوال تحويل البيانات بين النظام القديم (JSON) والجديد (MySQL)

import { Category as OldCategory, Subcategory as OldSubcategory, Product as OldProduct } from '../types/database';
import { Category as NewCategory, Subcategory as NewSubcategory, Product as NewProduct, CategoryInput, SubcategoryInput, ProductInput } from '../types/mysql-database';

// تحويل فئة من النظام القديم إلى الجديد
export function convertCategoryToMySQL(oldCategory: OldCategory): CategoryInput {
  return {
    id: oldCategory.id,
    name: oldCategory.name,
    name_ar: oldCategory.nameAr,
    description: oldCategory.description || null,
    description_ar: oldCategory.descriptionAr || null,
    image_url: oldCategory.image || null,
    is_active: oldCategory.isActive
  };
}



// تحويل فئة فرعية من النظام القديم إلى الجديد
export function convertSubcategoryToMySQL(oldSubcategory: OldSubcategory): SubcategoryInput {
  return {
    id: oldSubcategory.id,
    name: oldSubcategory.name,
    name_ar: oldSubcategory.nameAr,
    category_id: oldSubcategory.categoryId,
    description: oldSubcategory.description || null,
    description_ar: oldSubcategory.descriptionAr || null,
    is_active: oldSubcategory.isActive
  };
}



// تحويل منتج من النظام القديم إلى الجديد
export function convertProductToMySQL(oldProduct: OldProduct): {
  product: ProductInput;
  images: string[];
  features: string[];
  featuresAr: string[];
  specifications: Array<{
    nameEn: string;
    nameAr: string;
    valueEn: string;
    valueAr: string;
  }>;
} {
  return {
    product: {
      id: oldProduct.id,
      title: oldProduct.title,
      title_ar: oldProduct.titleAr,
      description: oldProduct.description || null,
      description_ar: oldProduct.descriptionAr || null,
      price: oldProduct.price,
      original_price: oldProduct.originalPrice || null,
      is_available: oldProduct.available,
      category_id: oldProduct.categoryId,
      subcategory_id: oldProduct.subcategoryId,
      is_active: oldProduct.isActive,
      is_featured: oldProduct.isFeatured
    },
    images: oldProduct.images || [],
    features: oldProduct.features || [],
    featuresAr: oldProduct.featuresAr || [],
    specifications: oldProduct.specifications || []
  };
}



// دالة لإنشاء ID من النص (مثل النظام القديم)
export function generateIdFromName(name: string): string {
  return name.toLowerCase()
    .replace(/[^a-z0-9]/g, '-')
    .replace(/-+/g, '-')
    .replace(/^-|-$/g, '');
}

// دالة لإنشاء ID رقمي للمنتجات
export function generateNumericId(): string {
  return Date.now().toString();
}

// دالة لتحويل التاريخ من string إلى Date
export function convertStringToDate(dateString: string): Date {
  return new Date(dateString);
}

// دالة لتحويل التاريخ من Date إلى string
export function convertDateToString(date: Date): string {
  return date.toISOString();
}
