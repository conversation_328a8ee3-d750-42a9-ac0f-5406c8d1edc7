// اختبار APIs الصفحة الرئيسية وصفحة المنتجات
const baseUrl = 'http://localhost:3000';

async function testCategoriesAPI() {
  console.log('🧪 اختبار API الفئات للصفحة الرئيسية...');
  
  try {
    const response = await fetch(`${baseUrl}/api/categories`);
    console.log(`📊 Status: ${response.status}`);
    
    if (response.ok) {
      const categories = await response.json();
      console.log(`✅ تم جلب الفئات بنجاح`);
      console.log(`📊 عدد الفئات: ${categories.length}`);
      
      if (categories.length > 0) {
        const firstCategory = categories[0];
        console.log('📋 مثال على فئة:');
        console.log(`   - ID: ${firstCategory.id}`);
        console.log(`   - الاسم بالعربية: ${firstCategory.name_ar}`);
        console.log(`   - الاسم بالإنجليزية: ${firstCategory.name}`);
        console.log(`   - نشطة: ${firstCategory.is_active ? 'نعم' : 'لا'}`);
        console.log(`   - صورة: ${firstCategory.image ? 'موجودة' : 'غير موجودة'}`);
      }
      return true;
    } else {
      console.log('❌ فشل في جلب الفئات');
      return false;
    }
  } catch (error) {
    console.error('❌ خطأ في اختبار API الفئات:', error.message);
    return false;
  }
}

async function testProductsAPI() {
  console.log('\n🧪 اختبار API المنتجات للصفحة الرئيسية...');
  
  try {
    const response = await fetch(`${baseUrl}/api/products`);
    console.log(`📊 Status: ${response.status}`);
    
    if (response.ok) {
      const products = await response.json();
      console.log(`✅ تم جلب المنتجات بنجاح`);
      console.log(`📊 عدد المنتجات: ${products.length}`);
      
      if (products.length > 0) {
        const firstProduct = products[0];
        console.log('📋 مثال على منتج:');
        console.log(`   - ID: ${firstProduct.id}`);
        console.log(`   - العنوان بالعربية: ${firstProduct.title_ar}`);
        console.log(`   - السعر: ${firstProduct.price} ر.س`);
        console.log(`   - متوفر: ${firstProduct.is_available ? 'نعم' : 'لا'}`);
        console.log(`   - مميز: ${firstProduct.is_featured ? 'نعم' : 'لا'}`);
        console.log(`   - عدد الصور: ${firstProduct.images?.length || 0}`);
        console.log(`   - عدد المميزات: ${firstProduct.features?.length || 0}`);
        console.log(`   - عدد المواصفات: ${firstProduct.specifications?.length || 0}`);
      }
      return true;
    } else {
      console.log('❌ فشل في جلب المنتجات');
      return false;
    }
  } catch (error) {
    console.error('❌ خطأ في اختبار API المنتجات:', error.message);
    return false;
  }
}

async function testFeaturedProductsAPI() {
  console.log('\n🧪 اختبار API المنتجات المميزة...');
  
  try {
    const response = await fetch(`${baseUrl}/api/products?featured=true`);
    console.log(`📊 Status: ${response.status}`);
    
    if (response.ok) {
      const featuredProducts = await response.json();
      console.log(`✅ تم جلب المنتجات المميزة بنجاح`);
      console.log(`📊 عدد المنتجات المميزة: ${featuredProducts.length}`);
      
      if (featuredProducts.length > 0) {
        console.log('📋 المنتجات المميزة:');
        featuredProducts.slice(0, 3).forEach((product, index) => {
          console.log(`   ${index + 1}. ${product.title_ar} - ${product.price} ر.س`);
        });
      } else {
        console.log('⚠️ لا توجد منتجات مميزة');
        console.log('💡 تلميح: يمكنك جعل منتج مميز من لوحة الإدارة');
      }
      return true;
    } else {
      console.log('❌ فشل في جلب المنتجات المميزة');
      return false;
    }
  } catch (error) {
    console.error('❌ خطأ في اختبار API المنتجات المميزة:', error.message);
    return false;
  }
}

async function testPageAccess() {
  console.log('\n🌐 اختبار الوصول للصفحات...');
  
  const pages = [
    { name: 'الصفحة الرئيسية', url: `${baseUrl}/ar` },
    { name: 'صفحة المنتجات', url: `${baseUrl}/ar/products` },
    { name: 'الصفحة الرئيسية (إنجليزي)', url: `${baseUrl}/en` },
    { name: 'صفحة المنتجات (إنجليزي)', url: `${baseUrl}/en/products` }
  ];

  for (const page of pages) {
    try {
      console.log(`\n📄 اختبار: ${page.name}`);
      const response = await fetch(page.url);
      console.log(`   Status: ${response.status}`);
      console.log(`   نتيجة: ${response.ok ? '✅ تحمل بنجاح' : '❌ فشل في التحميل'}`);
      
      if (response.ok) {
        console.log(`   🔗 يمكنك زيارة: ${page.url}`);
      }
    } catch (error) {
      console.log(`   ❌ خطأ: ${error.message}`);
    }
  }
}

async function checkDatabaseData() {
  console.log('\n📊 فحص البيانات في قاعدة البيانات...');
  
  try {
    // فحص الفئات
    const categoriesResponse = await fetch(`${baseUrl}/api/categories`);
    if (categoriesResponse.ok) {
      const categories = await categoriesResponse.json();
      const activeCategories = categories.filter(cat => cat.is_active);
      console.log(`📁 الفئات النشطة: ${activeCategories.length} من ${categories.length}`);
    }

    // فحص المنتجات
    const productsResponse = await fetch(`${baseUrl}/api/products`);
    if (productsResponse.ok) {
      const products = await productsResponse.json();
      const activeProducts = products.filter(prod => prod.is_active);
      const featuredProducts = products.filter(prod => prod.is_featured && prod.is_active);
      console.log(`📦 المنتجات النشطة: ${activeProducts.length} من ${products.length}`);
      console.log(`⭐ المنتجات المميزة: ${featuredProducts.length}`);
      
      // إحصائيات الصور والمميزات
      const productsWithImages = products.filter(prod => prod.images && prod.images.length > 0);
      const productsWithFeatures = products.filter(prod => prod.features && prod.features.length > 0);
      const productsWithSpecs = products.filter(prod => prod.specifications && prod.specifications.length > 0);
      
      console.log(`🖼️ منتجات بصور: ${productsWithImages.length}`);
      console.log(`⭐ منتجات بمميزات: ${productsWithFeatures.length}`);
      console.log(`📋 منتجات بمواصفات: ${productsWithSpecs.length}`);
    }
  } catch (error) {
    console.error('❌ خطأ في فحص البيانات:', error.message);
  }
}

async function main() {
  console.log('🚀 بدء اختبار APIs الصفحة الرئيسية وصفحة المنتجات\n');
  
  await checkDatabaseData();
  await testCategoriesAPI();
  await testProductsAPI();
  await testFeaturedProductsAPI();
  await testPageAccess();
  
  console.log('\n📋 ملخص الإصلاحات المطبقة:');
  console.log('1. ✅ تحديث CategoriesSection لاستخدام MySQL APIs');
  console.log('2. ✅ تحديث FeaturedProducts لاستخدام MySQL APIs');
  console.log('3. ✅ تحديث صفحة المنتجات لاستخدام MySQL APIs');
  console.log('4. ✅ إصلاح جميع خصائص البيانات (name_ar, title_ar, etc.)');
  console.log('5. ✅ إضافة دعم المنتجات المميزة في API');
  console.log('6. ✅ إصلاح عرض الصور باستخدام placeholder محلي');
  
  console.log('\n🎯 للتحقق من النتائج:');
  console.log('1. تأكد من تشغيل الخادم: npm run dev');
  console.log('2. زر الصفحة الرئيسية: http://localhost:3000/ar');
  console.log('3. زر صفحة المنتجات: http://localhost:3000/ar/products');
  console.log('4. تحقق من ظهور الفئات والمنتجات');
  
  console.log('\n💡 نصائح:');
  console.log('- إذا لم تظهر منتجات مميزة، اجعل بعض المنتجات مميزة من لوحة الإدارة');
  console.log('- تأكد من أن الفئات والمنتجات نشطة (is_active = true)');
  console.log('- تحقق من وجود صور للفئات والمنتجات');
  
  console.log('\n🏁 انتهاء اختبار APIs الصفحة الرئيسية');
}

main();
