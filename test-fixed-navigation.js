// اختبار النظام المحدث للتنقل والصور
const baseUrl = 'http://localhost:3000';

async function testImageDisplay() {
  console.log('🖼️ اختبار عرض الصور...\n');

  try {
    // اختبار placeholder API
    console.log('1. اختبار Placeholder API:');
    const placeholderResponse = await fetch(`${baseUrl}/api/placeholder?width=400&height=300&text=اختبار`);
    console.log(`   Status: ${placeholderResponse.status}`);
    console.log(`   Content-Type: ${placeholderResponse.headers.get('content-type')}`);
    console.log(`   نتيجة: ${placeholderResponse.ok ? '✅ يعمل' : '❌ لا يعمل'}`);

    // اختبار uploads API
    console.log('\n2. اختبار Uploads API:');
    const uploadsResponse = await fetch(`${baseUrl}/api/uploads/1715bc0d6c48e6cc75b8ab48e6c97dfb.jpg`);
    console.log(`   Status: ${uploadsResponse.status}`);
    console.log(`   Content-Type: ${uploadsResponse.headers.get('content-type')}`);
    console.log(`   نتيجة: ${uploadsResponse.ok ? '✅ يعمل' : '❌ لا يعمل'}`);

  } catch (error) {
    console.error('❌ خطأ في اختبار الصور:', error.message);
  }
}

async function testProductDetailsPage() {
  console.log('\n📦 اختبار صفحة تفاصيل المنتج...\n');

  try {
    // جلب منتج للاختبار
    const productsResponse = await fetch(`${baseUrl}/api/products`);
    if (productsResponse.ok) {
      const products = await productsResponse.json();
      if (products.length > 0) {
        const testProduct = products[0];
        console.log(`📋 اختبار منتج: ${testProduct.title_ar} (${testProduct.id})`);

        // اختبار API تفاصيل المنتج
        console.log('\n1. اختبار API تفاصيل المنتج:');
        const productResponse = await fetch(`${baseUrl}/api/products/${testProduct.id}`);
        console.log(`   Status: ${productResponse.status}`);
        
        if (productResponse.ok) {
          const productDetails = await productResponse.json();
          console.log('   ✅ API يعمل بنجاح');
          console.log(`   📊 البيانات:`);
          console.log(`      - العنوان: ${productDetails.title_ar}`);
          console.log(`      - السعر: ${productDetails.price} ر.س`);
          console.log(`      - متوفر: ${productDetails.is_available ? 'نعم' : 'لا'}`);
          console.log(`      - عدد الصور: ${productDetails.images?.length || 0}`);
          console.log(`      - عدد المميزات: ${productDetails.features?.length || 0}`);
          console.log(`      - عدد المواصفات: ${productDetails.specifications?.length || 0}`);
        } else {
          console.log('   ❌ API لا يعمل');
        }

        // اختبار صفحة تفاصيل المنتج
        console.log('\n2. اختبار صفحة تفاصيل المنتج:');
        const pageResponse = await fetch(`${baseUrl}/product-details/${testProduct.id}`);
        console.log(`   Status: ${pageResponse.status}`);
        console.log(`   نتيجة: ${pageResponse.ok ? '✅ تحمل بنجاح' : '❌ فشل في التحميل'}`);
        
        if (pageResponse.ok) {
          console.log(`   🔗 يمكنك زيارة: ${baseUrl}/product-details/${testProduct.id}`);
        }

      } else {
        console.log('⚠️ لا توجد منتجات للاختبار');
      }
    } else {
      console.log('❌ فشل في جلب المنتجات');
    }
  } catch (error) {
    console.error('❌ خطأ في اختبار صفحة تفاصيل المنتج:', error.message);
  }
}

async function testNavigationFlow() {
  console.log('\n🧭 اختبار مسار التنقل الكامل...\n');

  try {
    // جلب البيانات للاختبار
    const categoriesResponse = await fetch(`${baseUrl}/api/categories`);
    const subcategoriesResponse = await fetch(`${baseUrl}/api/subcategories`);
    const productsResponse = await fetch(`${baseUrl}/api/products`);

    if (categoriesResponse.ok && subcategoriesResponse.ok && productsResponse.ok) {
      const categories = await categoriesResponse.json();
      const subcategories = await subcategoriesResponse.json();
      const products = await productsResponse.json();

      console.log('📊 البيانات المتاحة:');
      console.log(`   - الفئات الرئيسية: ${categories.length}`);
      console.log(`   - الفئات الفرعية: ${subcategories.length}`);
      console.log(`   - المنتجات: ${products.length}`);

      if (categories.length > 0 && subcategories.length > 0 && products.length > 0) {
        const category = categories[0];
        const subcategory = subcategories[0];
        const product = products[0];

        console.log('\n🔗 مسار التنقل:');
        
        // 1. الصفحة الرئيسية
        console.log('1. الصفحة الرئيسية → عرض الفئات الرئيسية');
        const homeResponse = await fetch(`${baseUrl}/ar`);
        console.log(`   Status: ${homeResponse.status} ${homeResponse.ok ? '✅' : '❌'}`);
        console.log(`   URL: ${baseUrl}/ar`);

        // 2. صفحة الفئة الرئيسية
        console.log('\n2. الفئة الرئيسية → عرض الفئات الفرعية');
        const categoryResponse = await fetch(`${baseUrl}/ar/category/${category.id}`);
        console.log(`   Status: ${categoryResponse.status} ${categoryResponse.ok ? '✅' : '❌'}`);
        console.log(`   URL: ${baseUrl}/ar/category/${category.id}`);
        console.log(`   الفئة: ${category.name_ar}`);

        // 3. صفحة الفئة الفرعية
        console.log('\n3. الفئة الفرعية → عرض المنتجات');
        const subcategoryResponse = await fetch(`${baseUrl}/ar/subcategory/${subcategory.id}`);
        console.log(`   Status: ${subcategoryResponse.status} ${subcategoryResponse.ok ? '✅' : '❌'}`);
        console.log(`   URL: ${baseUrl}/ar/subcategory/${subcategory.id}`);
        console.log(`   الفئة الفرعية: ${subcategory.name_ar}`);

        // 4. صفحة تفاصيل المنتج
        console.log('\n4. المنتج → عرض التفاصيل');
        const productResponse = await fetch(`${baseUrl}/product-details/${product.id}`);
        console.log(`   Status: ${productResponse.status} ${productResponse.ok ? '✅' : '❌'}`);
        console.log(`   URL: ${baseUrl}/product-details/${product.id}`);
        console.log(`   المنتج: ${product.title_ar}`);

        console.log('\n🎯 مسار التنقل الكامل:');
        console.log(`الرئيسية → ${category.name_ar} → ${subcategory.name_ar} → ${product.title_ar}`);

      } else {
        console.log('⚠️ البيانات غير كافية لاختبار مسار التنقل الكامل');
      }
    } else {
      console.log('❌ فشل في جلب البيانات للاختبار');
    }
  } catch (error) {
    console.error('❌ خطأ في اختبار مسار التنقل:', error.message);
  }
}

async function testImageSources() {
  console.log('\n🔍 اختبار مصادر الصور...\n');

  try {
    // جلب منتج مع صور
    const productsResponse = await fetch(`${baseUrl}/api/products`);
    if (productsResponse.ok) {
      const products = await productsResponse.json();
      const productWithImages = products.find(p => p.images && p.images.length > 0);
      
      if (productWithImages) {
        console.log(`📦 منتج مع صور: ${productWithImages.title_ar}`);
        console.log(`📊 عدد الصور: ${productWithImages.images.length}`);
        
        // اختبار كل صورة
        for (let i = 0; i < Math.min(3, productWithImages.images.length); i++) {
          const image = productWithImages.images[i];
          console.log(`\n   🖼️ صورة ${i + 1}:`);
          console.log(`      URL: ${image.image_url}`);
          
          // اختبار الوصول للصورة
          try {
            const imageResponse = await fetch(`${baseUrl}${image.image_url}`);
            console.log(`      Status: ${imageResponse.status} ${imageResponse.ok ? '✅' : '❌'}`);
            console.log(`      Content-Type: ${imageResponse.headers.get('content-type')}`);
          } catch (error) {
            console.log(`      ❌ خطأ: ${error.message}`);
          }
        }
      } else {
        console.log('⚠️ لا توجد منتجات مع صور');
      }
    }
  } catch (error) {
    console.error('❌ خطأ في اختبار مصادر الصور:', error.message);
  }
}

async function main() {
  console.log('🚀 بدء اختبار النظام المحدث\n');
  
  await testImageDisplay();
  await testProductDetailsPage();
  await testNavigationFlow();
  await testImageSources();
  
  console.log('\n📋 ملخص الإصلاحات المطبقة:');
  console.log('1. ✅ إصلاح ProductCard لاستخدام /product-details/ بدلاً من /product/');
  console.log('2. ✅ إضافة معالجة أخطاء الصور في ProductCard');
  console.log('3. ✅ تحديث صفحة تفاصيل المنتج لاستخدام MySQL APIs');
  console.log('4. ✅ إصلاح عرض البيانات (title_ar, is_available, etc.)');
  console.log('5. ✅ إصلاح عرض الصور والمميزات والمواصفات');
  console.log('6. ✅ تحديث روابط واتساب والتنقل');
  
  console.log('\n🎯 النتيجة المتوقعة:');
  console.log('- ✅ الصور تظهر في جميع الصفحات');
  console.log('- ✅ عند الضغط على منتج → تفتح صفحة تفاصيل المنتج');
  console.log('- ✅ صفحة تفاصيل المنتج تعرض البيانات الصحيحة');
  console.log('- ✅ مسار التنقل يعمل بسلاسة');
  
  console.log('\n🔗 روابط للاختبار:');
  console.log('- الصفحة الرئيسية: http://localhost:3000/ar');
  console.log('- صفحة المنتجات: http://localhost:3000/ar/products');
  
  console.log('\n🏁 انتهاء اختبار النظام المحدث');
}

main();
