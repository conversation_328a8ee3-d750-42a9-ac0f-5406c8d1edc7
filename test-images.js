// اختبار عرض الصور في النظام
const baseUrl = 'http://localhost:3000';

async function testImageEndpoints() {
  console.log('🖼️ اختبار endpoints الصور...\n');

  // اختبار placeholder API
  console.log('1. اختبار placeholder API:');
  try {
    const placeholderResponse = await fetch(`${baseUrl}/api/placeholder?width=400&height=300&text=اختبار`);
    if (placeholderResponse.ok) {
      console.log('✅ Placeholder API يعمل بنجاح');
      console.log(`   Content-Type: ${placeholderResponse.headers.get('content-type')}`);
    } else {
      console.log('❌ Placeholder API لا يعمل:', placeholderResponse.status);
    }
  } catch (error) {
    console.log('❌ خطأ في Placeholder API:', error.message);
  }

  // اختبار uploads API
  console.log('\n2. اختبار uploads API:');
  try {
    // جلب قائمة المنتجات للحصول على أسماء الصور
    const productsResponse = await fetch(`${baseUrl}/api/admin/products`);
    if (productsResponse.ok) {
      const productsResult = await productsResponse.json();
      const products = productsResult.data || [];
      
      if (products.length > 0) {
        const firstProduct = products[0];
        if (firstProduct.images && firstProduct.images.length > 0) {
          const imageUrl = firstProduct.images[0].image_url;
          console.log(`   اختبار صورة: ${imageUrl}`);
          
          // استخراج اسم الملف من URL
          const fileName = imageUrl.split('/').pop();
          const testImageUrl = `${baseUrl}/api/uploads/${fileName}`;
          
          const imageResponse = await fetch(testImageUrl);
          if (imageResponse.ok) {
            console.log('✅ Uploads API يعمل بنجاح');
            console.log(`   Content-Type: ${imageResponse.headers.get('content-type')}`);
            console.log(`   Content-Length: ${imageResponse.headers.get('content-length')} bytes`);
          } else {
            console.log('❌ Uploads API لا يعمل:', imageResponse.status);
            console.log(`   URL المختبر: ${testImageUrl}`);
          }
        } else {
          console.log('⚠️ لا توجد صور في المنتجات للاختبار');
        }
      } else {
        console.log('⚠️ لا توجد منتجات للاختبار');
      }
    } else {
      console.log('❌ فشل في جلب المنتجات للاختبار');
    }
  } catch (error) {
    console.log('❌ خطأ في اختبار uploads API:', error.message);
  }

  // اختبار الصور المباشرة
  console.log('\n3. اختبار الصور المباشرة:');
  const testImages = [
    '1750643451808_3jaldpv9.jpg',
    '1750642917356_mqrr5hsb.webp',
    '1750642764105_wre5k8yh.jpg',
    '1750643160090_ih1qxb1y.jpg'
  ];

  for (const imageName of testImages) {
    try {
      const directUrl = `${baseUrl}/uploads/${imageName}`;
      const apiUrl = `${baseUrl}/api/uploads/${imageName}`;
      
      console.log(`   اختبار: ${imageName}`);
      
      // اختبار الرابط المباشر
      const directResponse = await fetch(directUrl);
      console.log(`     رابط مباشر: ${directResponse.ok ? '✅' : '❌'} (${directResponse.status})`);
      
      // اختبار API
      const apiResponse = await fetch(apiUrl);
      console.log(`     عبر API: ${apiResponse.ok ? '✅' : '❌'} (${apiResponse.status})`);
      
    } catch (error) {
      console.log(`     خطأ: ${error.message}`);
    }
  }
}

async function testImageDisplay() {
  console.log('\n🌐 اختبار عرض الصور في الصفحات...\n');

  const pages = [
    { name: 'الفئات الرئيسية', url: `${baseUrl}/admin/categories` },
    { name: 'الفئات الفرعية', url: `${baseUrl}/admin/subcategories` },
    { name: 'المنتجات', url: `${baseUrl}/admin/products` }
  ];

  for (const page of pages) {
    try {
      console.log(`اختبار صفحة: ${page.name}`);
      const response = await fetch(page.url);
      
      if (response.ok) {
        console.log(`✅ صفحة ${page.name} تحمل بنجاح`);
        console.log(`   Status: ${response.status}`);
        console.log(`   يمكنك زيارة: ${page.url}`);
      } else {
        console.log(`❌ صفحة ${page.name} لا تحمل: ${response.status}`);
      }
    } catch (error) {
      console.log(`❌ خطأ في تحميل صفحة ${page.name}: ${error.message}`);
    }
  }
}

async function checkImageFiles() {
  console.log('\n📁 فحص ملفات الصور...\n');
  
  const fs = require('fs');
  const path = require('path');
  
  try {
    const uploadsDir = path.join(process.cwd(), 'public', 'uploads');
    
    if (fs.existsSync(uploadsDir)) {
      const files = fs.readdirSync(uploadsDir);
      console.log(`📊 عدد الملفات في مجلد uploads: ${files.length}`);
      
      if (files.length > 0) {
        console.log('📋 أول 5 ملفات:');
        files.slice(0, 5).forEach((file, index) => {
          const filePath = path.join(uploadsDir, file);
          const stats = fs.statSync(filePath);
          console.log(`   ${index + 1}. ${file} (${Math.round(stats.size / 1024)} KB)`);
        });
      } else {
        console.log('⚠️ مجلد uploads فارغ');
      }
    } else {
      console.log('❌ مجلد uploads غير موجود');
    }
  } catch (error) {
    console.log('❌ خطأ في فحص ملفات الصور:', error.message);
  }
}

async function main() {
  console.log('🚀 بدء اختبار نظام الصور\n');
  
  await checkImageFiles();
  await testImageEndpoints();
  await testImageDisplay();
  
  console.log('\n📋 ملخص الحلول:');
  console.log('1. تم إنشاء API endpoint لخدمة الصور: /api/uploads/[...path]');
  console.log('2. تم إنشاء placeholder API محلي: /api/placeholder');
  console.log('3. تم إنشاء مكون SafeImage لمعالجة أخطاء الصور');
  console.log('4. تم تحديث إعدادات Next.js لدعم rewrites');
  console.log('5. تم تحديث جميع الصفحات لاستخدام المكونات الجديدة');
  
  console.log('\n🎯 للتأكد من عمل الصور:');
  console.log('1. تأكد من تشغيل الخادم: npm run dev');
  console.log('2. زر الصفحات وتحقق من ظهور الصور');
  console.log('3. إذا لم تظهر الصور، تحقق من console المتصفح');
  
  console.log('\n🏁 انتهاء اختبار نظام الصور');
}

main();
