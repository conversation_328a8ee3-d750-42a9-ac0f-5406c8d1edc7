// اختبار شامل لعمليات CRUD للمنتجات مع جميع الجداول المرتبطة
const baseUrl = 'http://localhost:3000';

// بيانات اختبار للمنتج مع جميع التفاصيل
const testProduct = {
  title: 'Test Product',
  titleAr: 'منتج تجريبي',
  description: 'This is a test product with all details',
  descriptionAr: 'هذا منتج تجريبي مع جميع التفاصيل',
  images: [
    'https://images.unsplash.com/photo-1586040140378-b5c5bfcf0b8b?w=400&h=300&fit=crop',
    'https://images.unsplash.com/photo-1544787219-7f47ccb76574?w=400&h=300&fit=crop'
  ],
  price: 299.99,
  originalPrice: 399.99,
  available: true,
  categoryId: 'cat-1', // يجب أن تكون موجودة في قاعدة البيانات
  subcategoryId: 'sub-1', // يجب أن تكون موجودة في قاعدة البيانات
  features: [
    'High quality material',
    'Durable construction',
    'Easy to clean'
  ],
  featuresAr: [
    'مواد عالية الجودة',
    'بناء متين',
    'سهل التنظيف'
  ],
  specifications: [
    {
      nameEn: 'Material',
      nameAr: 'المادة',
      valueEn: 'Stainless Steel',
      valueAr: 'فولاذ مقاوم للصدأ'
    },
    {
      nameEn: 'Size',
      nameAr: 'الحجم',
      valueEn: 'Large',
      valueAr: 'كبير'
    },
    {
      nameEn: 'Color',
      nameAr: 'اللون',
      valueEn: 'Silver',
      valueAr: 'فضي'
    }
  ],
  isActive: true,
  isFeatured: false
};

let createdProductId = null;

// اختبار إضافة منتج مع جميع التفاصيل
async function testAddProduct() {
  console.log('🧪 اختبار إضافة منتج مع جميع التفاصيل...');
  
  try {
    const response = await fetch(`${baseUrl}/api/admin/products`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(testProduct),
    });

    if (response.ok) {
      const result = await response.json();
      createdProductId = result.data.id;
      console.log('✅ تم إضافة المنتج بنجاح:', result.data.title_ar);
      console.log('📊 عدد الصور:', result.data.images.length);
      console.log('📊 عدد المميزات:', result.data.features.length);
      console.log('📊 عدد المواصفات:', result.data.specifications.length);
      return true;
    } else {
      const error = await response.json();
      console.error('❌ فشل في إضافة المنتج:', error.messageAr || error.message);
      return false;
    }
  } catch (error) {
    console.error('❌ خطأ في اختبار إضافة المنتج:', error.message);
    return false;
  }
}

// اختبار جلب جميع المنتجات مع التفاصيل
async function testGetProducts() {
  console.log('🧪 اختبار جلب جميع المنتجات مع التفاصيل...');
  
  try {
    const response = await fetch(`${baseUrl}/api/admin/products`);
    
    if (response.ok) {
      const result = await response.json();
      console.log('✅ تم جلب المنتجات بنجاح');
      console.log('📊 عدد المنتجات:', result.data.length);
      
      if (result.data.length > 0) {
        const firstProduct = result.data[0];
        console.log('📋 تفاصيل المنتج الأول:');
        console.log('  - العنوان:', firstProduct.title_ar);
        console.log('  - عدد الصور:', firstProduct.images?.length || 0);
        console.log('  - عدد المميزات:', firstProduct.features?.length || 0);
        console.log('  - عدد المواصفات:', firstProduct.specifications?.length || 0);
      }
      return true;
    } else {
      console.error('❌ فشل في جلب المنتجات:', response.status);
      return false;
    }
  } catch (error) {
    console.error('❌ خطأ في اختبار جلب المنتجات:', error.message);
    return false;
  }
}

// اختبار جلب منتج واحد مع التفاصيل
async function testGetSingleProduct() {
  if (!createdProductId) {
    console.log('❌ لا يوجد معرف منتج للاختبار');
    return false;
  }

  console.log('🧪 اختبار جلب منتج واحد مع التفاصيل...');
  
  try {
    const response = await fetch(`${baseUrl}/api/admin/products?id=${createdProductId}`);
    
    if (response.ok) {
      const result = await response.json();
      console.log('✅ تم جلب المنتج بنجاح');
      console.log('📋 تفاصيل المنتج:');
      console.log('  - العنوان:', result.data.title_ar);
      console.log('  - السعر:', result.data.price, 'ر.س');
      console.log('  - عدد الصور:', result.data.images.length);
      console.log('  - عدد المميزات:', result.data.features.length);
      console.log('  - عدد المواصفات:', result.data.specifications.length);
      return true;
    } else {
      console.error('❌ فشل في جلب المنتج:', response.status);
      return false;
    }
  } catch (error) {
    console.error('❌ خطأ في اختبار جلب المنتج:', error.message);
    return false;
  }
}

// اختبار تحديث منتج مع جميع التفاصيل
async function testUpdateProduct() {
  if (!createdProductId) {
    console.log('❌ لا يوجد معرف منتج للتحديث');
    return false;
  }

  console.log('🧪 اختبار تحديث منتج مع جميع التفاصيل...');
  
  try {
    const updateData = {
      titleAr: 'منتج تجريبي محدث',
      price: 199.99,
      images: [
        'https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=400&h=300&fit=crop'
      ],
      features: [
        'Updated feature 1',
        'Updated feature 2'
      ],
      featuresAr: [
        'ميزة محدثة 1',
        'ميزة محدثة 2'
      ],
      specifications: [
        {
          nameEn: 'Weight',
          nameAr: 'الوزن',
          valueEn: '2.5 kg',
          valueAr: '2.5 كيلو'
        }
      ],
      isFeatured: true
    };

    const response = await fetch(`${baseUrl}/api/admin/products?id=${createdProductId}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(updateData),
    });

    if (response.ok) {
      const result = await response.json();
      console.log('✅ تم تحديث المنتج بنجاح');
      console.log('📋 البيانات المحدثة:');
      console.log('  - العنوان الجديد:', result.data.title_ar);
      console.log('  - السعر الجديد:', result.data.price, 'ر.س');
      console.log('  - مميز:', result.data.is_featured ? 'نعم' : 'لا');
      console.log('  - عدد الصور الجديد:', result.data.images.length);
      console.log('  - عدد المميزات الجديد:', result.data.features.length);
      console.log('  - عدد المواصفات الجديد:', result.data.specifications.length);
      return true;
    } else {
      const error = await response.json();
      console.error('❌ فشل في تحديث المنتج:', error.messageAr || error.message);
      return false;
    }
  } catch (error) {
    console.error('❌ خطأ في اختبار تحديث المنتج:', error.message);
    return false;
  }
}

// اختبار تغيير حالة المنتج (نشط/غير نشط)
async function testToggleProductStatus() {
  if (!createdProductId) {
    console.log('❌ لا يوجد معرف منتج لتغيير الحالة');
    return false;
  }

  console.log('🧪 اختبار تغيير حالة المنتج...');
  
  try {
    const response = await fetch(`${baseUrl}/api/admin/products?id=${createdProductId}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        isActive: false
      }),
    });

    if (response.ok) {
      const result = await response.json();
      console.log('✅ تم تغيير حالة المنتج بنجاح');
      console.log('📋 الحالة الجديدة:', result.data.is_active ? 'نشط' : 'غير نشط');
      return true;
    } else {
      console.error('❌ فشل في تغيير حالة المنتج:', response.status);
      return false;
    }
  } catch (error) {
    console.error('❌ خطأ في اختبار تغيير حالة المنتج:', error.message);
    return false;
  }
}

// اختبار حذف منتج مع جميع التفاصيل
async function testDeleteProduct() {
  if (!createdProductId) {
    console.log('❌ لا يوجد معرف منتج للحذف');
    return false;
  }

  console.log('🧪 اختبار حذف منتج مع جميع التفاصيل...');
  
  try {
    const response = await fetch(`${baseUrl}/api/admin/products?id=${createdProductId}`, {
      method: 'DELETE',
    });

    if (response.ok) {
      console.log('✅ تم حذف المنتج وجميع تفاصيله بنجاح');
      return true;
    } else {
      const error = await response.json();
      console.error('❌ فشل في حذف المنتج:', error.messageAr || error.message);
      return false;
    }
  } catch (error) {
    console.error('❌ خطأ في اختبار حذف المنتج:', error.message);
    return false;
  }
}

// تشغيل جميع الاختبارات
async function runAllTests() {
  console.log('🚀 بدء اختبار عمليات CRUD للمنتجات مع جميع التفاصيل\n');
  
  console.log('📦 اختبار إدارة المنتجات:');
  await testAddProduct();
  await testGetProducts();
  await testGetSingleProduct();
  await testUpdateProduct();
  await testToggleProductStatus();
  await testDeleteProduct();
  
  console.log('\n🏁 انتهاء اختبارات المنتجات');
}

// تشغيل الاختبارات
runAllTests();
