import React from 'react';

interface AdminUser {
  id: number;
  username: string;
  email: string;
  role: string;
  isActive: boolean;
  lastLogin: Date | null;
  createdAt: Date;
  updatedAt: Date;
}

interface UserDetailsModalProps {
  isOpen: boolean;
  onClose: () => void;
  user: AdminUser | null;
}

const UserDetailsModal: React.FC<UserDetailsModalProps> = ({ isOpen, onClose, user }) => {
  if (!isOpen || !user) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-2xl shadow-2xl w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h2 className="text-xl font-bold text-gray-800 flex items-center">
            <i className="ri-user-line text-2xl ml-3 text-primary"></i>
            تفاصيل المستخدم
          </h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <i className="ri-close-line text-2xl"></i>
          </button>
        </div>

        {/* Content */}
        <div className="p-6 space-y-6">
          {/* User Avatar & Basic Info */}
          <div className="flex items-center space-x-4 space-x-reverse bg-gradient-to-r from-primary/10 to-secondary/10 p-6 rounded-2xl border border-primary/20">
            <div className="w-20 h-20 bg-gradient-to-r from-primary to-secondary rounded-2xl flex items-center justify-center shadow-lg">
              <i className="ri-user-line text-3xl text-white"></i>
            </div>
            <div className="flex-1">
              <h3 className="text-2xl font-bold text-gray-800 mb-1">{user.username}</h3>
              <p className="text-gray-600 mb-2 flex items-center">
                <i className="ri-mail-line text-lg ml-2 text-primary"></i>
                {user.email}
              </p>
              <div className="flex items-center space-x-3 space-x-reverse">
                <span className={`inline-flex px-3 py-1 text-sm font-semibold rounded-full ${
                  user.isActive 
                    ? 'bg-green-100 text-green-800' 
                    : 'bg-red-100 text-red-800'
                }`}>
                  {user.isActive ? 'نشط' : 'غير نشط'}
                </span>
                <span className="inline-block px-3 py-1 text-sm font-bold bg-gradient-to-r from-primary to-secondary text-white rounded-full">
                  مدير النظام
                </span>
              </div>
            </div>
          </div>

          {/* Details Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Account Information */}
            <div className="bg-gray-50 rounded-xl p-4">
              <h4 className="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                <i className="ri-information-line text-xl ml-2 text-primary"></i>
                معلومات الحساب
              </h4>
              <div className="space-y-3">
                <div className="flex justify-between items-center">
                  <span className="text-gray-600">معرف المستخدم:</span>
                  <span className="font-medium text-gray-800">#{user.id}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-gray-600">اسم المستخدم:</span>
                  <span className="font-medium text-gray-800">{user.username}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-gray-600">البريد الإلكتروني:</span>
                  <span className="font-medium text-gray-800">{user.email}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-gray-600">الدور:</span>
                  <span className="font-medium text-gray-800">مدير النظام</span>
                </div>
              </div>
            </div>

            {/* Activity Information */}
            <div className="bg-gray-50 rounded-xl p-4">
              <h4 className="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                <i className="ri-time-line text-xl ml-2 text-primary"></i>
                معلومات النشاط
              </h4>
              <div className="space-y-3">
                <div className="flex justify-between items-center">
                  <span className="text-gray-600">تاريخ الإنشاء:</span>
                  <span className="font-medium text-gray-800">
                    {new Date(user.createdAt).toLocaleDateString('ar-SA')}
                  </span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-gray-600">آخر تحديث:</span>
                  <span className="font-medium text-gray-800">
                    {new Date(user.updatedAt).toLocaleDateString('ar-SA')}
                  </span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-gray-600">آخر تسجيل دخول:</span>
                  <span className="font-medium text-gray-800">
                    {user.lastLogin 
                      ? new Date(user.lastLogin).toLocaleString('ar-SA')
                      : 'لم يسجل دخول بعد'
                    }
                  </span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-gray-600">الحالة:</span>
                  <span className={`font-medium ${user.isActive ? 'text-green-600' : 'text-red-600'}`}>
                    {user.isActive ? 'نشط' : 'غير نشط'}
                  </span>
                </div>
              </div>
            </div>
          </div>

          {/* Login History */}
          <div className="bg-gray-50 rounded-xl p-4">
            <h4 className="text-lg font-semibold text-gray-800 mb-4 flex items-center">
              <i className="ri-history-line text-xl ml-2 text-primary"></i>
              سجل تسجيل الدخول
            </h4>
            <div className="space-y-3">
              {user.lastLogin ? (
                <div className="flex items-center justify-between p-3 bg-white rounded-lg border">
                  <div className="flex items-center space-x-3 space-x-reverse">
                    <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                      <i className="ri-login-box-line text-green-600 text-sm"></i>
                    </div>
                    <div>
                      <p className="font-medium text-gray-800">آخر تسجيل دخول</p>
                      <p className="text-sm text-gray-600">
                        {new Date(user.lastLogin).toLocaleString('ar-SA')}
                      </p>
                    </div>
                  </div>
                  <span className="text-xs text-gray-500">
                    منذ {Math.floor((Date.now() - new Date(user.lastLogin).getTime()) / (1000 * 60 * 60 * 24))} يوم
                  </span>
                </div>
              ) : (
                <div className="text-center py-6">
                  <i className="ri-login-box-line text-3xl text-gray-400 mb-2"></i>
                  <p className="text-gray-500">لم يسجل دخول بعد</p>
                </div>
              )}
            </div>
          </div>

          {/* Security Information */}
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <div className="flex items-start">
              <i className="ri-shield-check-line text-blue-600 text-xl ml-2 mt-0.5"></i>
              <div className="text-sm text-blue-800">
                <p className="font-medium mb-1">معلومات الأمان:</p>
                <ul className="list-disc list-inside space-y-1 text-blue-700">
                  <li>كلمة المرور مشفرة باستخدام bcrypt</li>
                  <li>يتم تسجيل جميع عمليات تسجيل الدخول</li>
                  <li>الحساب محمي بنظام JWT tokens</li>
                  <li>يمكن تعطيل الحساب في أي وقت</li>
                </ul>
              </div>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="flex justify-end p-6 border-t border-gray-200">
          <button
            onClick={onClose}
            className="px-6 py-3 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors"
          >
            إغلاق
          </button>
        </div>
      </div>
    </div>
  );
};

export default UserDetailsModal;
