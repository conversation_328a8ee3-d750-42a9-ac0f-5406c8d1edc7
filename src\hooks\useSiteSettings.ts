import { useState, useEffect } from 'react';
import { SiteSettings } from '../types/admin';
import { getSiteSettings } from '../data/settings';

/**
 * Hook مخصص لاستخدام إعدادات الموقع
 * يقوم بجلب الإعدادات من التخزين المحلي ويعيد تحديثها عند تغييرها
 */
export const useSiteSettings = () => {
  const [settings, setSettings] = useState<SiteSettings | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const loadSettings = async () => {
      try {
        // محاولة جلب الإعدادات من API أولاً
        const response = await fetch('/api/settings');

        if (response.ok) {
          const data = await response.json();
          setSettings(data);
        } else {
          // في حالة فشل API، استخدام الإعدادات المحلية
          console.warn('Failed to fetch settings from API, using local settings');
          const localSettings = getSiteSettings();
          setSettings(localSettings);
        }
      } catch (error) {
        console.error('Error loading site settings:', error);
        // في حالة الخطأ، استخدام الإعدادات المحلية
        const localSettings = getSiteSettings();
        setSettings(localSettings);
      } finally {
        setLoading(false);
      }
    };

    loadSettings();

    // الاستماع لتغييرات التخزين المحلي
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === 'siteSettings') {
        loadSettings();
      }
    };

    // الاستماع لحدث مخصص لإعادة تحميل الإعدادات
    const handleSettingsUpdate = () => {
      loadSettings();
    };

    window.addEventListener('storage', handleStorageChange);
    window.addEventListener('siteSettingsUpdated', handleSettingsUpdate);

    return () => {
      window.removeEventListener('storage', handleStorageChange);
      window.removeEventListener('siteSettingsUpdated', handleSettingsUpdate);
    };
  }, []);

  // دالة لإعادة تحميل الإعدادات يدوياً
  const reload = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/settings');

      if (response.ok) {
        const data = await response.json();
        setSettings(data);
      } else {
        const localSettings = getSiteSettings();
        setSettings(localSettings);
      }
    } catch (error) {
      console.error('Error reloading settings:', error);
      const localSettings = getSiteSettings();
      setSettings(localSettings);
    } finally {
      setLoading(false);
    }
  };

  return {
    settings,
    loading,
    reload
  };
};

/**
 * Hook مبسط للحصول على إعدادات محددة
 */
export const useHeaderSettings = () => {
  const { settings, loading } = useSiteSettings();
  return {
    headerSettings: settings?.headerSettings,
    loading
  };
};

export const useFooterSettings = () => {
  const { settings, loading } = useSiteSettings();
  return {
    footerSettings: settings?.footerSettings,
    loading
  };
};

export const useSocialLinks = () => {
  const { settings, loading } = useSiteSettings();
  return {
    socialLinks: settings?.socialLinks,
    loading
  };
};

export const useContactInfo = () => {
  const { settings, loading } = useSiteSettings();
  return {
    contactInfo: {
      email: settings?.contactEmail,
      phone: settings?.phone,
      whatsapp: settings?.whatsappNumber,
      address: settings?.address,
      addressAr: settings?.addressAr,
      workingHours: settings?.workingHours,
      workingHoursAr: settings?.workingHoursAr
    },
    loading
  };
};

export const useCompanyInfo = () => {
  const { settings, loading } = useSiteSettings();
  return {
    companyInfo: {
      name: settings?.siteName,
      nameAr: settings?.siteNameAr,
      about: settings?.aboutText,
      aboutAr: settings?.aboutTextAr,
      email: settings?.contactEmail,
      phone: settings?.phone,
      whatsapp: settings?.whatsappNumber,
      address: settings?.address,
      addressAr: settings?.addressAr,
      workingHours: settings?.workingHours,
      workingHoursAr: settings?.workingHoursAr
    },
    loading
  };
};
