import { NextApiRequest, NextApiResponse } from 'next';
import { getCategories, addCategory } from '../../../lib/mysql-database';
import { Category } from '../../../types/mysql-database';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    switch (req.method) {
      case 'GET':
        const categories = await getCategories();
        res.status(200).json(categories);
        break;

      case 'POST':
        const { name, nameAr, description, descriptionAr, image, isActive } = req.body;

        if (!name || !nameAr) {
          return res.status(400).json({
            success: false,
            message: 'Name and Arabic name are required'
          });
        }

        const categoryData = {
          id: Date.now().toString(), // سيتم استبداله بـ UUID في MySQL
          name,
          name_ar: nameAr,
          description: description || null,
          description_ar: descriptionAr || null,
          image: image || null,
          is_active: isActive !== undefined ? isActive : true
        };

        const newCategory = await addCategory(categoryData);
        res.status(201).json(newCategory);
        break;

      default:
        res.setHeader('Allow', ['GET', 'POST']);
        res.status(405).end(`Method ${req.method} Not Allowed`);
    }
  } catch (error) {
    console.error('API Error:', error);
    res.status(500).json({ error: 'Internal Server Error' });
  }
}
