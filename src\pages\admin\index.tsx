import { useEffect } from 'react';
import { useRouter } from 'next/router';

// دالة للتحقق من تسجيل الدخول
const isLoggedIn = () => {
  if (typeof window !== 'undefined') {
    const token = localStorage.getItem('authToken') ||
                 document.cookie.split('; ').find(row => row.startsWith('authToken='))?.split('=')[1];
    return !!token;
  }
  return false;
};

const AdminIndex = () => {
  const router = useRouter();

  useEffect(() => {
    // التحقق من تسجيل الدخول وتوجيه المستخدم
    if (isLoggedIn()) {
      router.push('/admin/dashboard');
    } else {
      router.push('/admin/login');
    }
  }, [router]);

  // عرض شاشة تحميل أثناء التوجيه
  return (
    <div className="min-h-screen bg-gray-100 flex items-center justify-center">
      <div className="text-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600 mx-auto mb-4"></div>
        <p className="text-gray-600">جاري التحميل...</p>
      </div>
    </div>
  );
};

export default AdminIndex;
