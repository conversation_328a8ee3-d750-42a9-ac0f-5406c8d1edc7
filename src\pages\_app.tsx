import '../styles/tailwind.css';
import '../styles/globals.css';
import '../styles/admin.css';
import '../../public/remixicon.css';
import type { AppProps } from 'next/app';
import { appWithTranslation } from 'next-i18next';
import { Toaster } from 'react-hot-toast';

function MyApp({ Component, pageProps }: AppProps) {
  return (
    <>
      <Component {...pageProps} />
      <Toaster
        position="top-center"
        reverseOrder={false}
        gutter={8}
        containerClassName=""
        containerStyle={{}}
        toastOptions={{
          // إعدادات افتراضية لجميع التوستات
          className: '',
          duration: 4000,
          style: {
            background: '#363636',
            color: '#fff',
            fontFamily: 'Arial, sans-serif',
            fontSize: '14px',
            borderRadius: '8px',
            padding: '12px 16px',
            boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
          },
          // إعدادات للنجاح
          success: {
            duration: 4000,
            style: {
              background: '#10b981',
              color: '#fff',
            },
            iconTheme: {
              primary: '#fff',
              secondary: '#10b981',
            },
          },
          // إعدادات للخطأ
          error: {
            duration: 5000,
            style: {
              background: '#ef4444',
              color: '#fff',
            },
            iconTheme: {
              primary: '#fff',
              secondary: '#ef4444',
            },
          },
          // إعدادات للتحميل
          loading: {
            duration: Infinity,
            style: {
              background: '#3b82f6',
              color: '#fff',
            },
          },
        }}
      />
    </>
  );
}

export default appWithTranslation(MyApp);
