import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import ProductCard from './ProductCard';
import { useTranslation } from 'next-i18next';
import { Product } from '../types/database';
import { productsApi } from '../lib/api';

const FeaturedProducts = () => {
  const { t } = useTranslation('common');
  const [featuredProducts, setFeaturedProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchFeaturedProducts = async () => {
      try {
        const products = await productsApi.getFeatured();
        setFeaturedProducts(products.slice(0, 8)); // أول 8 منتجات مميزة
      } catch (error) {
        console.error('Error fetching featured products:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchFeaturedProducts();
  }, []);

  if (loading) {
    return (
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-primary mb-4">{t('featured.title')}</h2>
            <p className="text-gray-600 max-w-2xl mx-auto">
              {t('featured.subtitle')}
            </p>
          </div>
          <div className="text-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-gray-600">جاري تحميل المنتجات المميزة...</p>
          </div>
        </div>
      </section>
    );
  }

  return (
    <section className="py-16 bg-white">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold text-primary mb-4">{t('featured.title')}</h2>
          <p className="text-gray-600 max-w-2xl mx-auto">
            {t('featured.subtitle')}
          </p>
        </div>
        {featuredProducts.length > 0 ? (
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8">
            {featuredProducts.map((product) => (
              <ProductCard
                key={product.id}
                id={parseInt(product.id)}
                image={product.images[0]}
                title={product.titleAr}
                description={product.descriptionAr}
                price={product.price}
                available={product.available}
              />
            ))}
          </div>
        ) : (
          <div className="text-center py-12">
            <p className="text-gray-600">لا توجد منتجات مميزة حالياً</p>
          </div>
        )}
        <div className="text-center mt-12">
          <Link href="/products" className="bg-primary text-white px-8 py-3 rounded-button font-medium hover:bg-primary/90 transition-colors inline-block whitespace-nowrap">
            {t('featured.viewAll')}
          </Link>
        </div>
      </div>
    </section>
  );
};

export default FeaturedProducts;
