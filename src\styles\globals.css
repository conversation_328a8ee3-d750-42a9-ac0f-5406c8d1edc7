:where([class^="ri-"])::before { content: "\f3c2"; }
body {
  direction: rtl;
  text-align: right;
  font-family: '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', sans-serif;
}
.ltr {
direction: ltr;
text-align: left;
}
.rtl {
direction: rtl;
text-align: right;
}
.slider-container {
overflow: hidden;
position: relative;
}
.slider {
display: flex;
transition: transform 0.5s ease-in-out;
}
.slide {
min-width: 100%;
}
.slider-nav {
position: absolute;
bottom: 20px;
left: 50%;
transform: translateX(-50%);
display: flex;
gap: 8px;
}
.slider-dot {
width: 12px;
height: 12px;
border-radius: 50%;
background-color: rgba(255, 255, 255, 0.5);
cursor: pointer;
}
.slider-dot.active {
background-color: white;
}
.partners-slider {
animation: slidePartners 30s linear infinite;
}
@keyframes slidePartners {
0% { transform: translateX(0); }
100% { transform: translateX(-100%); }
}
input[type="number"]::-webkit-inner-spin-button,
input[type="number"]::-webkit-outer-spin-button {
-webkit-appearance: none;
margin: 0;
}
.category-item:hover .category-overlay {
opacity: 1;
}
.product-card:hover .product-actions {
opacity: 1;
}
.language-switch {
position: relative;
}
.language-dropdown {
display: none;
position: absolute;
top: 100%;
right: 0;
background-color: white;
border-radius: 8px;
box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
z-index: 50;
min-width: 120px;
}
.language-switch:hover .language-dropdown {
display: block;
}

/* تحسينات إضافية للتصميم */
.product-card {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.product-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.product-actions {
  transition: all 0.3s ease;
}

/* تأثيرات الأزرار */
.btn-primary {
  background: linear-gradient(135deg, #1B1B3A 0%, #4A4A7D 100%);
  transition: all 0.3s ease;
}

.btn-primary:hover {
  background: linear-gradient(135deg, #4A4A7D 0%, #1B1B3A 100%);
  transform: translateY(-2px);
  box-shadow: 0 10px 20px rgba(27, 27, 58, 0.3);
}

/* تحسين الانتقالات */
* {
  transition: color 0.3s ease, background-color 0.3s ease, border-color 0.3s ease, transform 0.3s ease, box-shadow 0.3s ease;
}

/* تحسين التمرير */
html {
  scroll-behavior: smooth;
}

/* تحسين التركيز */
button:focus,
input:focus,
textarea:focus,
select:focus {
  outline: 2px solid #1B1B3A;
  outline-offset: 2px;
}

/* تحسين الرسوم المتحركة */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fadeInUp {
  animation: fadeInUp 0.6s ease-out;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* أنيميشن الرسالة المنبثقة */
@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(100px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-slide-up {
  animation: slideUp 0.3s ease-out;
}

/* تأثيرات حديثة إضافية */
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

.animate-float {
  animation: float 6s ease-in-out infinite;
}

@keyframes glow {
  0% {
    box-shadow: 0 0 20px rgba(14, 165, 233, 0.5);
  }
  100% {
    box-shadow: 0 0 30px rgba(14, 165, 233, 0.8);
  }
}

.animate-glow {
  animation: glow 2s ease-in-out infinite alternate;
}

@keyframes gradient-shift {
  0%, 100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

.animate-gradient-shift {
  background-size: 200% 200%;
  animation: gradient-shift 3s ease infinite;
}

@keyframes category-slide-in {
  0% {
    opacity: 0;
    transform: translateY(30px) scale(0.9);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.animate-category-slide-in {
  animation: category-slide-in 0.6s ease-out forwards;
}

@keyframes icon-bounce {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-5px);
  }
}

.animate-icon-bounce {
  animation: icon-bounce 2s infinite;
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

.animate-shimmer {
  animation: shimmer 2s linear infinite;
}

/* تأثيرات الخلفية */
.bg-hero-pattern {
  background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%239C92AC' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='4'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
}

/* تأثيرات الزجاج المصقول */
.glass-effect {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.glass-dark {
  background: rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* تأثيرات التدرج المتقدمة */
.gradient-primary {
  background: linear-gradient(135deg, #0ea5e9 0%, #0284c7 50%, #0369a1 100%);
}

.gradient-secondary {
  background: linear-gradient(135deg, #d946ef 0%, #c026d3 50%, #a21caf 100%);
}

.gradient-accent {
  background: linear-gradient(135deg, #f97316 0%, #ea580c 50%, #c2410c 100%);
}

.gradient-rainbow {
  background: linear-gradient(135deg, #0ea5e9 0%, #d946ef 25%, #f97316 50%, #10b981 75%, #0ea5e9 100%);
}

/* تدرجات إضافية للنصوص */
.text-gradient-primary {
  background: linear-gradient(135deg, #0ea5e9, #0284c7);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.text-gradient-secondary {
  background: linear-gradient(135deg, #d946ef, #c026d3);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.text-gradient-rainbow {
  background: linear-gradient(135deg, #0ea5e9, #d946ef, #f97316, #10b981);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  background-size: 200% 200%;
  animation: gradient-shift 3s ease infinite;
}

/* تأثيرات الظلال المتقدمة */
.shadow-glow {
  box-shadow: 0 0 20px rgba(14, 165, 233, 0.3);
}

.shadow-glow-lg {
  box-shadow: 0 0 40px rgba(14, 165, 233, 0.4);
}

.shadow-inner-glow {
  box-shadow: inset 0 0 20px rgba(14, 165, 233, 0.2);
}

/* تحسينات الأزرار */
.btn-modern {
  position: relative;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.btn-modern::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.btn-modern:hover::before {
  left: 100%;
}

.btn-modern:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

/* تأثيرات البطاقات */
.card-modern {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.card-modern::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.card-modern:hover::before {
  opacity: 1;
}

.card-modern:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

/* تحسينات النصوص */
.text-gradient {
  background: linear-gradient(135deg, #0ea5e9, #d946ef);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.text-glow {
  text-shadow: 0 0 10px rgba(14, 165, 233, 0.5);
}

/* تأثيرات التحميل */
.loading-shimmer {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}

/* تحسينات الاستجابة */
@media (max-width: 768px) {
  .card-modern:hover {
    transform: translateY(-4px) scale(1.01);
  }

  .btn-modern:hover {
    transform: translateY(-1px);
  }
}

/* تأثيرات إضافية للصفحة الرئيسية */
.hero-title {
  background: linear-gradient(135deg, #ffffff 0%, #0ea5e9 50%, #ffffff 100%);
  background-size: 200% 200%;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: gradient-shift 4s ease infinite;
}

.section-divider {
  position: relative;
  height: 2px;
  background: linear-gradient(90deg, transparent, #0ea5e9, transparent);
  margin: 4rem 0;
}

.section-divider::before {
  content: '';
  position: absolute;
  top: -4px;
  left: 50%;
  transform: translateX(-50%);
  width: 12px;
  height: 12px;
  background: #0ea5e9;
  border-radius: 50%;
  box-shadow: 0 0 20px rgba(14, 165, 233, 0.5);
}

/* تأثيرات التمرير المتقدمة */
.scroll-reveal {
  opacity: 0;
  transform: translateY(50px);
  transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

.scroll-reveal.revealed {
  opacity: 1;
  transform: translateY(0);
}

/* تأثيرات الخلفية المتحركة */
.animated-bg {
  background: linear-gradient(-45deg, #0ea5e9, #d946ef, #f97316, #10b981);
  background-size: 400% 400%;
  animation: gradient-shift 15s ease infinite;
}

/* تأثيرات النصوص المتقدمة */
.text-shadow-glow {
  text-shadow: 0 0 20px rgba(14, 165, 233, 0.5), 0 0 40px rgba(14, 165, 233, 0.3);
}

.text-outline {
  -webkit-text-stroke: 2px rgba(255, 255, 255, 0.3);
  text-stroke: 2px rgba(255, 255, 255, 0.3);
}

/* تأثيرات الأيقونات */
.icon-float {
  animation: icon-bounce 3s ease-in-out infinite;
}

.icon-rotate {
  animation: spin 8s linear infinite;
}

.icon-pulse {
  animation: pulse 2s ease-in-out infinite;
}

/* تأثيرات الحدود المتقدمة */
.border-gradient {
  border: 2px solid transparent;
  background: linear-gradient(white, white) padding-box,
              linear-gradient(135deg, #0ea5e9, #d946ef) border-box;
}

.border-animated {
  position: relative;
  border: 2px solid transparent;
}

.border-animated::before {
  content: '';
  position: absolute;
  inset: 0;
  padding: 2px;
  background: linear-gradient(135deg, #0ea5e9, #d946ef, #f97316);
  border-radius: inherit;
  mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  mask-composite: exclude;
  animation: gradient-shift 3s ease infinite;
}

/* تأثيرات التحميل المتقدمة */
.skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}

.loading-dots {
  display: inline-flex;
  gap: 4px;
}

.loading-dots span {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #0ea5e9;
  animation: loading-bounce 1.4s ease-in-out infinite both;
}

.loading-dots span:nth-child(1) { animation-delay: -0.32s; }
.loading-dots span:nth-child(2) { animation-delay: -0.16s; }

@keyframes loading-bounce {
  0%, 80%, 100% {
    transform: scale(0);
  }
  40% {
    transform: scale(1);
  }
}

/* تأثيرات الانتقال المتقدمة */
.page-transition {
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

.fade-in {
  animation: fadeIn 0.8s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.slide-in-left {
  animation: slideInLeft 0.8s ease-out;
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.slide-in-right {
  animation: slideInRight 0.8s ease-out;
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* تأثيرات التفاعل المتقدمة */
.interactive-card {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
}

.interactive-card:hover {
  transform: translateY(-8px) rotateX(5deg);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.magnetic-button {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.magnetic-button:hover {
  transform: scale(1.05);
  box-shadow: 0 10px 30px rgba(14, 165, 233, 0.3);
}

/* تحسينات الأداء */
.gpu-accelerated {
  transform: translateZ(0);
  will-change: transform;
}

.smooth-scroll {
  scroll-behavior: smooth;
}

/* تأثيرات الشاشات الكبيرة */
@media (min-width: 1200px) {
  .hero-title {
    font-size: 8rem;
  }

  .section-title {
    font-size: 4rem;
  }
}
