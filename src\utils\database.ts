import fs from 'fs';
import path from 'path';
import { Category, Subcategory, AdminProduct, SiteSettings, AdminUser } from '../types/admin';
import { readEncryptedDatabase, writeEncryptedDatabase } from '../lib/secure-database';

// مسار ملف قاعدة البيانات القديم (للتوافق)
const DB_PATH = path.join(process.cwd(), 'src/data/database.json');

// واجهة قاعدة البيانات
interface Database {
  categories: Category[];
  subcategories: Subcategory[];
  products: AdminProduct[];
  settings: SiteSettings;
  adminUser: AdminUser;
}

// قراءة قاعدة البيانات (مؤقتاً من الملف العادي)
export const readDatabase = (): Database => {
  try {
    // قراءة مباشرة من الملف العادي لحل المشكلة الفورية
    if (fs.existsSync(DB_PATH)) {
      const data = fs.readFileSync(DB_PATH, 'utf8');
      return JSON.parse(data);
    }

    throw new Error('Database file not found');
  } catch (error) {
    console.error('Error reading database:', error);
    throw new Error('Failed to read database');
  }
};

// كتابة قاعدة البيانات (مؤقتاً إلى الملف العادي)
export const writeDatabase = (data: Database): void => {
  try {
    fs.writeFileSync(DB_PATH, JSON.stringify(data, null, 2), 'utf8');
  } catch (error) {
    console.error('Error writing database:', error);
    throw new Error('Failed to write database');
  }
};

// دوال الفئات الرئيسية
export const getCategories = (): Category[] => {
  const db = readDatabase();
  return db.categories;
};

export const getCategoryById = (id: string): Category | null => {
  const categories = getCategories();
  return categories.find(cat => cat.id === id) || null;
};

export const addCategory = (category: Omit<Category, 'id' | 'createdAt' | 'updatedAt'>): Category => {
  const db = readDatabase();

  // إنشاء ID من الاسم الإنجليزي
  const id = category.name.toLowerCase()
    .replace(/[^a-z0-9]/g, '-')
    .replace(/-+/g, '-')
    .replace(/^-|-$/g, '');

  const newCategory: Category = {
    ...category,
    id,
    createdAt: new Date(),
    updatedAt: new Date()
  };

  db.categories.push(newCategory);
  writeDatabase(db);
  return newCategory;
};

export const updateCategory = (id: string, updates: Partial<Category>): Category | null => {
  const db = readDatabase();
  const categoryIndex = db.categories.findIndex(cat => cat.id === id);
  
  if (categoryIndex === -1) return null;
  
  db.categories[categoryIndex] = {
    ...db.categories[categoryIndex],
    ...updates,
    updatedAt: new Date()
  };
  
  writeDatabase(db);
  return db.categories[categoryIndex];
};

export const deleteCategory = (id: string): boolean => {
  const db = readDatabase();
  const categoryIndex = db.categories.findIndex(cat => cat.id === id);
  
  if (categoryIndex === -1) return false;
  
  // حذف الفئات الفرعية المرتبطة
  db.subcategories = db.subcategories.filter(sub => sub.categoryId !== id);
  
  // حذف المنتجات المرتبطة
  db.products = db.products.filter(product => product.categoryId !== id);
  
  // حذف الفئة
  db.categories.splice(categoryIndex, 1);
  
  writeDatabase(db);
  return true;
};

// دوال الفئات الفرعية
export const getSubcategories = (): Subcategory[] => {
  const db = readDatabase();
  return db.subcategories;
};

export const getSubcategoriesByCategory = (categoryId: string): Subcategory[] => {
  const subcategories = getSubcategories();
  return subcategories.filter(sub => sub.categoryId === categoryId);
};

export const getSubcategoryById = (id: string): Subcategory | null => {
  const subcategories = getSubcategories();
  return subcategories.find(sub => sub.id === id) || null;
};

export const addSubcategory = (subcategory: Omit<Subcategory, 'id' | 'createdAt' | 'updatedAt'>): Subcategory => {
  const db = readDatabase();

  // إنشاء ID من الاسم الإنجليزي
  const id = subcategory.name.toLowerCase()
    .replace(/[^a-z0-9]/g, '-')
    .replace(/-+/g, '-')
    .replace(/^-|-$/g, '');

  const newSubcategory: Subcategory = {
    ...subcategory,
    id,
    createdAt: new Date(),
    updatedAt: new Date()
  };

  db.subcategories.push(newSubcategory);
  writeDatabase(db);
  return newSubcategory;
};

export const updateSubcategory = (id: string, updates: Partial<Subcategory>): Subcategory | null => {
  const db = readDatabase();
  const subcategoryIndex = db.subcategories.findIndex(sub => sub.id === id);
  
  if (subcategoryIndex === -1) return null;
  
  db.subcategories[subcategoryIndex] = {
    ...db.subcategories[subcategoryIndex],
    ...updates,
    updatedAt: new Date()
  };
  
  writeDatabase(db);
  return db.subcategories[subcategoryIndex];
};

export const deleteSubcategory = (id: string): boolean => {
  const db = readDatabase();
  const subcategoryIndex = db.subcategories.findIndex(sub => sub.id === id);
  
  if (subcategoryIndex === -1) return false;
  
  // حذف المنتجات المرتبطة
  db.products = db.products.filter(product => product.subcategoryId !== id);
  
  // حذف الفئة الفرعية
  db.subcategories.splice(subcategoryIndex, 1);
  
  writeDatabase(db);
  return true;
};

// دوال المنتجات
export const getProducts = (): AdminProduct[] => {
  const db = readDatabase();
  return db.products;
};

export const getProductById = (id: string): AdminProduct | null => {
  const products = getProducts();
  return products.find(product => product.id === id) || null;
};

export const getProductsByCategory = (categoryId: string): AdminProduct[] => {
  const products = getProducts();
  return products.filter(product => product.categoryId === categoryId);
};

export const getProductsBySubcategory = (subcategoryId: string): AdminProduct[] => {
  const products = getProducts();
  return products.filter(product => product.subcategoryId === subcategoryId);
};

export const getFeaturedProducts = (): AdminProduct[] => {
  const products = getProducts();
  return products.filter(product => product.isFeatured && product.isActive);
};

export const addProduct = (product: Omit<AdminProduct, 'id' | 'createdAt' | 'updatedAt'>): AdminProduct => {
  const db = readDatabase();

  // إنشاء ID متسلسل
  const maxId = db.products.reduce((max, p) => {
    const id = parseInt(p.id);
    return isNaN(id) ? max : Math.max(max, id);
  }, 0);

  const newProduct: AdminProduct = {
    ...product,
    id: (maxId + 1).toString(),
    createdAt: new Date(),
    updatedAt: new Date()
  };

  db.products.push(newProduct);
  writeDatabase(db);
  return newProduct;
};

export const updateProduct = (id: string, updates: Partial<AdminProduct>): AdminProduct | null => {
  const db = readDatabase();
  const productIndex = db.products.findIndex(product => product.id === id);
  
  if (productIndex === -1) return null;
  
  db.products[productIndex] = {
    ...db.products[productIndex],
    ...updates,
    updatedAt: new Date()
  };
  
  writeDatabase(db);
  return db.products[productIndex];
};

export const deleteProduct = (id: string): boolean => {
  const db = readDatabase();
  const productIndex = db.products.findIndex(product => product.id === id);
  
  if (productIndex === -1) return false;
  
  db.products.splice(productIndex, 1);
  writeDatabase(db);
  return true;
};

// دوال الإعدادات
export const getSettings = (): SiteSettings => {
  const db = readDatabase();
  return db.settings;
};

export const updateSettings = (updates: Partial<SiteSettings>): SiteSettings => {
  const db = readDatabase();
  db.settings = {
    ...db.settings,
    ...updates
  };
  
  writeDatabase(db);
  return db.settings;
};

// دوال المستخدم الإداري - تم نقلها إلى النظام الآمن
// استخدم الآن: src/lib/secure-storage.ts

// دوال الإحصائيات
export const getStats = () => {
  const db = readDatabase();
  
  return {
    totalProducts: db.products.length,
    totalCategories: db.categories.length,
    totalSubcategories: db.subcategories.length,
    activeProducts: db.products.filter(p => p.isActive).length,
    inactiveProducts: db.products.filter(p => !p.isActive).length,
    featuredProducts: db.products.filter(p => p.isFeatured).length,
    availableProducts: db.products.filter(p => p.available).length,
    unavailableProducts: db.products.filter(p => !p.available).length
  };
};
