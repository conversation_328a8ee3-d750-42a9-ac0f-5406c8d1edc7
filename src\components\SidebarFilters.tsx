import React, { useState, useEffect } from 'react';
import { useTranslation } from 'next-i18next';
import { Category, Subcategory } from '../types/database';
import { categoriesApi, subcategoriesApi } from '../lib/api';

interface SidebarFiltersProps {
  selectedCategory: string;
  setSelectedCategory: (category: string) => void;
  selectedSubcategory: string;
  setSelectedSubcategory: (subcategory: string) => void;
  priceRange: string;
  setPriceRange: (range: string) => void;
  availability: string[];
  setAvailability: (availability: string[]) => void;
}

const SidebarFilters: React.FC<SidebarFiltersProps> = ({
  selectedCategory,
  setSelectedCategory,
  selectedSubcategory,
  setSelectedSubcategory,
  priceRange,
  setPriceRange,
  availability,
  setAvailability
}) => {
  const { t } = useTranslation('common');
  const [categories, setCategories] = useState<Category[]>([]);
  const [subcategories, setSubcategories] = useState<Subcategory[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchCategories = async () => {
      try {
        const categoriesData = await categoriesApi.getAll();
        setCategories(categoriesData);
      } catch (error) {
        console.error('Error fetching categories:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchCategories();
  }, []);

  useEffect(() => {
    const fetchSubcategories = async () => {
      if (selectedCategory === 'all') {
        setSubcategories([]);
        return;
      }

      try {
        const subcategoriesData = await subcategoriesApi.getByCategory(selectedCategory);
        setSubcategories(subcategoriesData);
      } catch (error) {
        console.error('Error fetching subcategories:', error);
        setSubcategories([]);
      }
    };

    fetchSubcategories();
  }, [selectedCategory]);

  const prices = [
    { value: 'all', label: t('filters.allPrices') },
    { value: 'low', label: t('filters.under1000') },
    { value: 'medium', label: t('filters.between1000_5000') },
    { value: 'high', label: t('filters.over5000') },
  ];

  const availabilityOptions = [
    { value: 'in-stock', label: t('filters.inStock') },
    { value: 'out-stock', label: t('filters.outOfStock') },
  ];

  const handleAvailabilityChange = (value: string) => {
    setAvailability(
      availability.includes(value)
        ? availability.filter((v) => v !== value)
        : [...availability, value]
    );
  };

  if (loading) {
    return (
      <aside className="bg-white rounded-lg shadow-md p-6 sticky top-24">
        <div className="text-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-gray-600">جاري تحميل الفلاتر...</p>
        </div>
      </aside>
    );
  }

  return (
    <aside className="bg-white rounded-lg shadow-md p-6 sticky top-24">
      {/* الفئات الرئيسية */}
      <h3 className="text-lg font-bold text-gray-800 mb-4">{t('filters.mainCategories')}</h3>
      <div className="space-y-3">
        <label className="flex items-center gap-2 cursor-pointer">
          <input
            type="radio"
            name="category"
            value="all"
            checked={selectedCategory === 'all'}
            onChange={() => {
              setSelectedCategory('all');
              setSelectedSubcategory('all');
            }}
            className="hidden"
          />
          <div className="w-4 h-4 border border-gray-300 rounded-full flex items-center justify-center">
            <div className={`w-2 h-2 bg-primary rounded-full ${selectedCategory === 'all' ? '' : 'hidden'}`}></div>
          </div>
          <span className="text-gray-700">{t('filters.allProducts')}</span>
        </label>
        {categories.map((category) => (
          <label key={category.id} className="flex items-center gap-2 cursor-pointer">
            <input
              type="radio"
              name="category"
              value={category.id}
              checked={selectedCategory === category.id}
              onChange={() => {
                setSelectedCategory(category.id);
                setSelectedSubcategory('all'); // إعادة تعيين الفئة الفرعية
              }}
              className="hidden"
            />
            <div className="w-4 h-4 border border-gray-300 rounded-full flex items-center justify-center">
              <div className={`w-2 h-2 bg-primary rounded-full ${selectedCategory === category.id ? '' : 'hidden'}`}></div>
            </div>
            <span className="text-gray-700">{category.nameAr}</span>
          </label>
        ))}
      </div>

      {/* الفئات الفرعية */}
      {subcategories.length > 0 && (
        <>
          <hr className="my-6 border-gray-200" />
          <h3 className="text-lg font-bold text-gray-800 mb-4">{t('filters.subcategories')}</h3>
          <div className="space-y-3">
            <label className="flex items-center gap-2 cursor-pointer">
              <input
                type="radio"
                name="subcategory"
                value="all"
                checked={selectedSubcategory === 'all'}
                onChange={() => setSelectedSubcategory('all')}
                className="hidden"
              />
              <div className="w-4 h-4 border border-gray-300 rounded-full flex items-center justify-center">
                <div className={`w-2 h-2 bg-primary rounded-full ${selectedSubcategory === 'all' ? '' : 'hidden'}`}></div>
              </div>
              <span className="text-gray-700">{t('filters.allSubcategories')}</span>
            </label>
            {subcategories.map((subcategory) => (
              <label key={subcategory.id} className="flex items-center gap-2 cursor-pointer">
                <input
                  type="radio"
                  name="subcategory"
                  value={subcategory.id}
                  checked={selectedSubcategory === subcategory.id}
                  onChange={() => setSelectedSubcategory(subcategory.id)}
                  className="hidden"
                />
                <div className="w-4 h-4 border border-gray-300 rounded-full flex items-center justify-center">
                  <div className={`w-2 h-2 bg-primary rounded-full ${selectedSubcategory === subcategory.id ? '' : 'hidden'}`}></div>
                </div>
                <span className="text-gray-700">{subcategory.nameAr}</span>
              </label>
            ))}
          </div>
        </>
      )}

      <hr className="my-6 border-gray-200" />
      <h3 className="text-lg font-bold text-gray-800 mb-4">{t('filters.price')}</h3>
      <div className="space-y-3">
        {prices.map((price) => (
          <label key={price.value} className="flex items-center gap-2 cursor-pointer">
            <input
              type="radio"
              name="price"
              value={price.value}
              checked={priceRange === price.value}
              onChange={() => setPriceRange(price.value)}
              className="hidden"
            />
            <div className="w-4 h-4 border border-gray-300 rounded-full flex items-center justify-center">
              <div className={`w-2 h-2 bg-primary rounded-full ${priceRange === price.value ? '' : 'hidden'}`}></div>
            </div>
            <span className="text-gray-700">{price.label}</span>
          </label>
        ))}
      </div>
      <hr className="my-6 border-gray-200" />
      <h3 className="text-lg font-bold text-gray-800 mb-4">{t('filters.availability')}</h3>
      <div className="space-y-3">
        {availabilityOptions.map((avail) => (
          <label key={avail.value} className="flex items-center gap-2 cursor-pointer">
            <input
              type="checkbox"
              name="availability"
              value={avail.value}
              checked={availability.includes(avail.value)}
              onChange={() => handleAvailabilityChange(avail.value)}
              className="hidden"
            />
            <div className="w-4 h-4 border border-gray-300 rounded flex items-center justify-center">
              <div className={`w-2 h-2 bg-primary rounded ${availability.includes(avail.value) ? '' : 'hidden'}`}></div>
            </div>
            <span className="text-gray-700">{avail.label}</span>
          </label>
        ))}
      </div>

      {/* زر مسح الفلاتر */}
      <hr className="my-6 border-gray-200" />
      <button
        onClick={() => {
          setSelectedCategory('all');
          setSelectedSubcategory('all');
          setPriceRange('all');
          setAvailability(['in-stock', 'out-stock']);
        }}
        className="w-full bg-gray-100 text-gray-700 py-2 px-4 rounded-lg hover:bg-gray-200 transition-colors text-sm font-medium"
      >
        {t('filters.clearAll')}
      </button>
    </aside>
  );
};

export default SidebarFilters;
