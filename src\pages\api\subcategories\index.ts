import { NextApiRequest, NextApiResponse } from 'next';
import { getSubcategories, getSubcategoriesByCategory, addSubcategory } from '../../../lib/mysql-database';
import { Subcategory } from '../../../types/mysql-database';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    switch (req.method) {
      case 'GET':
        const { categoryId } = req.query;

        let subcategories: Subcategory[];
        if (categoryId && typeof categoryId === 'string') {
          subcategories = await getSubcategoriesByCategory(categoryId);
        } else {
          subcategories = await getSubcategories();
        }

        res.status(200).json(subcategories);
        break;

      case 'POST':
        const { name, nameAr, categoryId: catId, description, descriptionAr, image, isActive } = req.body;

        if (!name || !nameAr || !catId) {
          return res.status(400).json({
            success: false,
            message: 'Name, Arabic name, and category ID are required'
          });
        }

        const subcategoryData = {
          id: Date.now().toString(), // سيتم استبداله بـ UUID في MySQL
          name,
          name_ar: nameAr,
          category_id: catId,
          description: description || null,
          description_ar: descriptionAr || null,
          image: image || null,
          is_active: isActive !== undefined ? isActive : true
        };

        const newSubcategory = await addSubcategory(subcategoryData);
        res.status(201).json(newSubcategory);
        break;

      default:
        res.setHeader('Allow', ['GET', 'POST']);
        res.status(405).end(`Method ${req.method} Not Allowed`);
    }
  } catch (error) {
    console.error('API Error:', error);
    res.status(500).json({ error: 'Internal Server Error' });
  }
}
