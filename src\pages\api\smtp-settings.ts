import { NextApiRequest, NextApiResponse } from 'next';
import { getContactInfo, updateContactInfo } from '../../lib/mysql-database';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method === 'GET') {
    try {
      const contactInfo = await getContactInfo();
      
      // إرجاع الإعدادات مع API Key للتشخيص
      res.status(200).json({
        success: true,
        settings: {
          email: contactInfo?.email || '',
          hasPassword: !!contactInfo?.Password,
          apiKey: contactInfo?.Password || 'غير موجود',
          apiType: 'resend',
          service: 'Resend API'
        }
      });
    } catch (error) {
      console.error('Error fetching SMTP settings:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to fetch SMTP settings'
      });
    }
  } else if (req.method === 'POST') {
    try {
      const { email, password } = req.body;

      if (!email || !password) {
        return res.status(400).json({
          success: false,
          error: 'Email and password are required'
        });
      }

      // تحديث إعدادات SMTP في قاعدة البيانات
      await updateContactInfo(email, password);

      res.status(200).json({
        success: true,
        message: 'SMTP settings updated successfully'
      });
    } catch (error) {
      console.error('Error updating SMTP settings:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to update SMTP settings'
      });
    }
  } else {
    res.setHeader('Allow', ['GET', 'POST']);
    res.status(405).json({
      success: false,
      error: `Method ${req.method} not allowed`
    });
  }
}
