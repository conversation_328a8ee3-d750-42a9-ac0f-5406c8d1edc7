import React, { useState, useRef } from 'react';

interface HeroImageUploadProps {
  images: string[];
  onImagesChange: (images: string[]) => void;
  maxImages?: number;
}

const HeroImageUpload: React.FC<HeroImageUploadProps> = ({ 
  images, 
  onImagesChange, 
  maxImages = 5 
}) => {
  const [uploading, setUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState<{ [key: string]: number }>({});
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileSelect = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (!files || files.length === 0) return;

    // التحقق من عدد الصور
    if (images.length + files.length > maxImages) {
      alert(`يمكن رفع ${maxImages} صور كحد أقصى`);
      return;
    }

    setUploading(true);
    const newImages: string[] = [];

    try {
      for (let i = 0; i < files.length; i++) {
        const file = files[i];
        
        // التحقق من نوع الملف
        if (!file.type.startsWith('image/')) {
          alert(`الملف ${file.name} ليس صورة صالحة`);
          continue;
        }

        // التحقق من حجم الملف (10MB)
        if (file.size > 10 * 1024 * 1024) {
          alert(`الملف ${file.name} كبير جداً. الحد الأقصى 10MB`);
          continue;
        }

        const uploadId = `upload-${Date.now()}-${i}`;
        setUploadProgress(prev => ({ ...prev, [uploadId]: 0 }));

        try {
          const imageUrl = await uploadImage(file, (progress) => {
            setUploadProgress(prev => ({ ...prev, [uploadId]: progress }));
          });
          
          newImages.push(imageUrl);
          setUploadProgress(prev => ({ ...prev, [uploadId]: 100 }));
        } catch (error) {
          console.error('خطأ في رفع الصورة:', error);
          alert(`فشل في رفع الصورة ${file.name}`);
        }
      }

      // إضافة الصور الجديدة إلى القائمة
      onImagesChange([...images, ...newImages]);

    } finally {
      setUploading(false);
      setUploadProgress({});
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    }
  };

  const uploadImage = async (file: File, onProgress?: (progress: number) => void): Promise<string> => {
    return new Promise((resolve, reject) => {
      const formData = new FormData();
      formData.append('image', file);

      // الحصول على التوكن
      const token = localStorage.getItem('authToken') || 
                   document.cookie.split('; ').find(row => row.startsWith('authToken='))?.split('=')[1];

      const xhr = new XMLHttpRequest();

      // تتبع التقدم
      xhr.upload.addEventListener('progress', (event) => {
        if (event.lengthComputable && onProgress) {
          const progress = Math.round((event.loaded / event.total) * 100);
          onProgress(progress);
        }
      });

      xhr.addEventListener('load', () => {
        if (xhr.status === 200) {
          try {
            const response = JSON.parse(xhr.responseText);
            if (response.success) {
              resolve(response.imageUrl);
            } else {
              reject(new Error(response.messageAr || response.message || 'فشل في رفع الصورة'));
            }
          } catch (error) {
            reject(new Error('خطأ في معالجة الاستجابة'));
          }
        } else {
          reject(new Error(`خطأ في الخادم: ${xhr.status}`));
        }
      });

      xhr.addEventListener('error', () => {
        reject(new Error('خطأ في الشبكة'));
      });

      xhr.open('POST', '/api/upload-image');
      xhr.setRequestHeader('Authorization', `Bearer ${token}`);
      xhr.send(formData);
    });
  };

  const removeImage = async (index: number) => {
    const imageUrl = images[index];

    // إذا كانت الصورة مرفوعة محلياً، احذفها من الخادم
    if (imageUrl.startsWith('/uploads/')) {
      try {
        const token = localStorage.getItem('authToken') ||
                     document.cookie.split('; ').find(row => row.startsWith('authToken='))?.split('=')[1];

        const response = await fetch('/api/delete-image', {
          method: 'DELETE',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`
          },
          body: JSON.stringify({ imageUrl })
        });

        if (!response.ok) {
          const errorData = await response.json();
          console.warn('فشل في حذف الصورة من الخادم:', errorData.messageAr || errorData.message);
        } else {
          console.log('✅ تم حذف الصورة من الخادم بنجاح');
        }
      } catch (error) {
        console.warn('خطأ في حذف الصورة من الخادم:', error);
      }
    }

    // حذف الصورة من القائمة
    const newImages = images.filter((_, i) => i !== index);
    onImagesChange(newImages);
  };

  const moveImage = (fromIndex: number, toIndex: number) => {
    const newImages = [...images];
    const [movedImage] = newImages.splice(fromIndex, 1);
    newImages.splice(toIndex, 0, movedImage);
    onImagesChange(newImages);
  };

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h4 className="text-md font-medium text-gray-700">صور الهيرو ({images.length}/{maxImages})</h4>
        <button
          type="button"
          onClick={() => fileInputRef.current?.click()}
          disabled={uploading || images.length >= maxImages}
          className="bg-primary text-white px-4 py-2 rounded-lg hover:bg-primary/90 disabled:opacity-50 disabled:cursor-not-allowed flex items-center transition-colors"
        >
          {uploading ? (
            <>
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white ml-2"></div>
              جاري الرفع...
            </>
          ) : (
            <>
              <i className="ri-upload-2-line text-lg ml-2"></i>
              إضافة صور
            </>
          )}
        </button>
      </div>

      <input
        ref={fileInputRef}
        type="file"
        accept="image/*"
        multiple
        onChange={handleFileSelect}
        className="hidden"
      />

      {/* عرض تقدم الرفع */}
      {Object.keys(uploadProgress).length > 0 && (
        <div className="space-y-2">
          {Object.entries(uploadProgress).map(([uploadId, progress]) => (
            <div key={uploadId} className="bg-gray-100 rounded-lg p-3">
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm text-gray-600">جاري رفع الصورة...</span>
                <span className="text-sm font-medium text-gray-800">{progress}%</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div 
                  className="bg-primary h-2 rounded-full transition-all duration-300"
                  style={{ width: `${progress}%` }}
                ></div>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* عرض الصور */}
      {images.length > 0 && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {images.map((image, index) => (
            <div key={index} className="relative group bg-gray-100 rounded-lg overflow-hidden">
              <div className="aspect-video relative">
                <img
                  src={image}
                  alt={`صورة هيرو ${index + 1}`}
                  className="w-full h-full object-cover"
                  onError={(e) => {
                    const target = e.target as HTMLImageElement;
                    target.src = '/images/placeholder.svg';
                  }}
                />
                
                {/* أزرار التحكم */}
                <div className="absolute inset-0 bg-black bg-opacity-50 opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex items-center justify-center">
                  <div className="flex space-x-2 space-x-reverse">
                    {index > 0 && (
                      <button
                        type="button"
                        onClick={() => moveImage(index, index - 1)}
                        className="bg-white text-gray-800 p-2 rounded-full hover:bg-gray-100 transition-colors"
                        title="تحريك لليسار"
                      >
                        <i className="ri-arrow-left-line"></i>
                      </button>
                    )}
                    
                    {index < images.length - 1 && (
                      <button
                        type="button"
                        onClick={() => moveImage(index, index + 1)}
                        className="bg-white text-gray-800 p-2 rounded-full hover:bg-gray-100 transition-colors"
                        title="تحريك لليمين"
                      >
                        <i className="ri-arrow-right-line"></i>
                      </button>
                    )}
                    
                    <button
                      type="button"
                      onClick={() => removeImage(index)}
                      className="bg-red-500 text-white p-2 rounded-full hover:bg-red-600 transition-colors"
                      title="حذف الصورة"
                    >
                      <i className="ri-delete-bin-line"></i>
                    </button>
                  </div>
                </div>
              </div>
              
              <div className="p-3">
                <p className="text-sm text-gray-600 truncate">صورة هيرو {index + 1}</p>
                <p className="text-xs text-gray-500 mt-1">{image}</p>
              </div>
            </div>
          ))}
        </div>
      )}

      {images.length === 0 && (
        <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center">
          <i className="ri-image-line text-4xl text-gray-400 mb-4"></i>
          <p className="text-gray-600 mb-2">لا توجد صور هيرو</p>
          <p className="text-sm text-gray-500">اضغط على "إضافة صور" لرفع صور جديدة</p>
        </div>
      )}

      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <div className="flex items-start">
          <i className="ri-information-line text-blue-600 text-lg ml-2 mt-0.5"></i>
          <div className="text-sm text-blue-800">
            <p className="font-medium mb-1">نصائح لصور الهيرو:</p>
            <ul className="space-y-1 text-blue-700">
              <li>• استخدم صور عالية الجودة بنسبة عرض إلى ارتفاع 16:9</li>
              <li>• الحد الأقصى لحجم الصورة: 10MB</li>
              <li>• الصيغ المدعومة: JPG, PNG, WebP</li>
              <li>• يمكن إعادة ترتيب الصور بالسحب والإفلات</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
};

export default HeroImageUpload;
