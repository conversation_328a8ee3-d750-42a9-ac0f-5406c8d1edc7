// اختبار سريع لاتصال قاعدة البيانات
const mysql = require('mysql2/promise');

async function testConnection() {
  let connection = null;
  try {
    console.log('🔄 اختبار اتصال قاعدة البيانات...');

    connection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      port: process.env.DB_PORT || 3306,
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || '',
      database: process.env.DB_NAME || 'droobhajer_db'
    });

    console.log('✅ تم الاتصال بقاعدة البيانات بنجاح');

    // اختبار جلب الفئات
    const [categories] = await connection.execute('SELECT * FROM categories LIMIT 5');
    console.log(`📊 عدد الفئات: ${categories.length}`);
    
    // اختبار جلب المنتجات
    const [products] = await connection.execute('SELECT * FROM products LIMIT 5');
    console.log(`📦 عدد المنتجات: ${products.length}`);
    
    // اختبار جلب صور المنتجات
    const [images] = await connection.execute('SELECT * FROM product_images LIMIT 5');
    console.log(`🖼️ عدد الصور: ${images.length}`);
    
    // اختبار جلب مميزات المنتجات
    const [features] = await connection.execute('SELECT * FROM product_features LIMIT 5');
    console.log(`⭐ عدد المميزات: ${features.length}`);
    
    // اختبار جلب مواصفات المنتجات
    const [specs] = await connection.execute('SELECT * FROM product_specifications LIMIT 5');
    console.log(`📋 عدد المواصفات: ${specs.length}`);

    if (categories.length === 0) {
      console.log('⚠️ لا توجد بيانات في قاعدة البيانات. يرجى تشغيل ملف sample_data.sql');
    }

  } catch (error) {
    console.error('❌ خطأ في الاتصال بقاعدة البيانات:', error.message);
    console.log('\n🔧 تأكد من:');
    console.log('1. تشغيل خادم MySQL');
    console.log('2. صحة بيانات الاتصال في ملف .env.local');
    console.log('3. وجود قاعدة البيانات droobhajer_db');
    console.log('4. إعادة تشغيل خادم MySQL إذا كان هناك "Too many connections"');
  } finally {
    if (connection) {
      try {
        await connection.end();
        console.log('✅ تم إغلاق الاتصال بنجاح');
      } catch (closeError) {
        console.log('⚠️ تحذير: مشكلة في إغلاق الاتصال');
      }
    }
  }
}

testConnection();
