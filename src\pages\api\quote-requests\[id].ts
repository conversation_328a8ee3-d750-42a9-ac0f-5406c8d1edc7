import { NextApiRequest, NextApiResponse } from 'next';
import fs from 'fs';
import path from 'path';
import {
  getQuoteRequestById,
  updateQuoteRequest,
  addQuoteRequestLog,
  getQuoteRequestWithDetails
} from '../../../lib/mysql-database';

interface QuoteRequest {
  id: string;
  customerInfo: {
    name: string;
    email: string;
    phone: string;
    company: string;
  };
  products: any[];
  totalAmount: number;
  createdAt: string;
  status: 'pending' | 'processed' | 'sent';
  excelFilePath: string;
}

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  const { id } = req.query;

  if (req.method === 'PATCH') {
    try {
      const { status, notes } = req.body;

      // التحقق من وجود الطلب في قاعدة البيانات
      const existingRequest = await getQuoteRequestById(id as string);
      if (!existingRequest) {
        return res.status(404).json({
          success: false,
          message: 'الطلب غير موجود'
        });
      }

      // تحديث بيانات الطلب في قاعدة البيانات
      const updateData: any = {};
      if (status !== undefined) updateData.status = status;
      if (notes !== undefined) updateData.notes = notes;

      const updatedRequest = await updateQuoteRequest(id as string, updateData);

      // إضافة سجل للتحديث إذا تم تغيير الحالة
      if (status && status !== existingRequest.status) {
        await addQuoteRequestLog(
          id as string,
          'admin', // يمكن تحسين هذا لاحقاً لاستخدام معرف المدير الفعلي
          'status_change',
          `تم تغيير الحالة من ${existingRequest.status} إلى ${status}`
        );
      }

      res.status(200).json({
        success: true,
        message: 'تم تحديث الطلب بنجاح',
        request: updatedRequest
      });

    } catch (error) {
      console.error('Error updating quote request:', error);
      res.status(500).json({
        success: false,
        message: 'حدث خطأ أثناء تحديث الطلب'
      });
    }
  } else if (req.method === 'GET') {
    try {
      // جلب طلب التسعير مع التفاصيل من قاعدة البيانات
      const quoteRequest = await getQuoteRequestWithDetails(id as string);

      if (!quoteRequest) {
        return res.status(404).json({
          success: false,
          message: 'الطلب غير موجود'
        });
      }

      res.status(200).json({
        success: true,
        request: quoteRequest
      });

    } catch (error) {
      console.error('Error fetching quote request:', error);
      res.status(500).json({
        success: false,
        message: 'حدث خطأ أثناء جلب الطلب'
      });
    }
  } else {
    res.setHeader('Allow', ['GET', 'PATCH']);
    res.status(405).end(`Method ${req.method} Not Allowed`);
  }
}
