'use client';

import { useState, useEffect } from 'react';
import { useParams } from 'next/navigation';
import { Locale } from '../../../lib/i18n';
import { getSessionId, getSessionInfo, createNewSession } from '../../../src/lib/browser-session';
import { useCart } from '../../../src/lib/session-cart';
import Navbar from '../../../components/Navbar';
import Footer from '../../../components/Footer';

export default function TestSessionsPage() {
  const params = useParams();
  const locale = (params?.locale || 'ar') as Locale;
  
  const { cart, addToCart, clearCart, itemCount, total } = useCart();
  const [sessionInfo, setSessionInfo] = useState<any>(null);
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
    updateSessionInfo();
  }, []);

  const updateSessionInfo = () => {
    if (typeof window !== 'undefined') {
      setSessionInfo(getSessionInfo());
    }
  };

  const handleAddTestProduct = () => {
    const testProduct = {
      id: `test_${Date.now()}`,
      title: `منتج تجريبي ${Date.now()}`,
      titleAr: `منتج تجريبي ${Date.now()}`,
      image: '/api/placeholder?width=200&height=200&text=Test',
      price: Math.floor(Math.random() * 1000) + 100,
    };

    addToCart(testProduct);
    updateSessionInfo();
  };

  const handleCreateNewSession = () => {
    createNewSession();
    updateSessionInfo();
    window.location.reload(); // إعادة تحميل الصفحة لتطبيق الجلسة الجديدة
  };

  if (!mounted) {
    return <div>جاري التحميل...</div>;
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Navbar />
      
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto">
          <h1 className="text-3xl font-bold text-center mb-8">
            اختبار نظام الجلسات المتعددة
          </h1>

          <div className="grid md:grid-cols-2 gap-8">
            {/* معلومات الجلسة */}
            <div className="bg-white rounded-xl shadow-lg p-6">
              <h2 className="text-xl font-bold mb-4 text-primary">
                <i className="ri-user-line ml-2"></i>
                معلومات الجلسة الحالية
              </h2>
              
              {sessionInfo && (
                <div className="space-y-3">
                  <div className="bg-gray-50 p-3 rounded-lg">
                    <strong>معرف الجلسة:</strong>
                    <br />
                    <code className="text-sm text-blue-600 break-all">
                      {sessionInfo.sessionId}
                    </code>
                  </div>
                  
                  <div className="bg-gray-50 p-3 rounded-lg">
                    <strong>عدد المفاتيح المحفوظة:</strong>
                    <span className="ml-2 text-green-600 font-bold">
                      {sessionInfo.keysCount}
                    </span>
                  </div>

                  {sessionInfo.keys && sessionInfo.keys.length > 0 && (
                    <div className="bg-gray-50 p-3 rounded-lg">
                      <strong>المفاتيح:</strong>
                      <ul className="mt-2 text-sm">
                        {sessionInfo.keys.map((key: string, index: number) => (
                          <li key={index} className="text-gray-600">
                            • {key}
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}
                </div>
              )}

              <button
                onClick={updateSessionInfo}
                className="mt-4 w-full bg-blue-500 text-white py-2 px-4 rounded-lg hover:bg-blue-600 transition-colors"
              >
                <i className="ri-refresh-line ml-1"></i>
                تحديث المعلومات
              </button>

              <button
                onClick={handleCreateNewSession}
                className="mt-2 w-full bg-red-500 text-white py-2 px-4 rounded-lg hover:bg-red-600 transition-colors"
              >
                <i className="ri-add-line ml-1"></i>
                إنشاء جلسة جديدة
              </button>
            </div>

            {/* عربة التسوق */}
            <div className="bg-white rounded-xl shadow-lg p-6">
              <h2 className="text-xl font-bold mb-4 text-primary">
                <i className="ri-shopping-cart-line ml-2"></i>
                عربة التسوق ({itemCount} منتج)
              </h2>

              <div className="space-y-3 mb-4">
                <div className="bg-gray-50 p-3 rounded-lg">
                  <strong>عدد المنتجات:</strong>
                  <span className="ml-2 text-green-600 font-bold">{itemCount}</span>
                </div>
                
                <div className="bg-gray-50 p-3 rounded-lg">
                  <strong>إجمالي السعر:</strong>
                  <span className="ml-2 text-green-600 font-bold">{total} ر.س</span>
                </div>
              </div>

              <div className="space-y-2 mb-4">
                <button
                  onClick={handleAddTestProduct}
                  className="w-full bg-green-500 text-white py-2 px-4 rounded-lg hover:bg-green-600 transition-colors"
                >
                  <i className="ri-add-line ml-1"></i>
                  إضافة منتج تجريبي
                </button>

                <button
                  onClick={clearCart}
                  className="w-full bg-red-500 text-white py-2 px-4 rounded-lg hover:bg-red-600 transition-colors"
                  disabled={itemCount === 0}
                >
                  <i className="ri-delete-bin-line ml-1"></i>
                  تفريغ العربة
                </button>
              </div>

              {cart.length > 0 && (
                <div className="border-t pt-4">
                  <h3 className="font-bold mb-2">المنتجات في العربة:</h3>
                  <div className="space-y-2 max-h-40 overflow-y-auto">
                    {cart.map((item) => (
                      <div key={item.id} className="bg-gray-50 p-2 rounded text-sm">
                        <div className="font-medium">{item.title}</div>
                        <div className="text-gray-600">
                          الكمية: {item.quantity} × {item.price} ر.س
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* تعليمات الاختبار */}
          <div className="mt-8 bg-blue-50 border border-blue-200 rounded-xl p-6">
            <h3 className="text-lg font-bold text-blue-800 mb-3">
              <i className="ri-information-line ml-2"></i>
              كيفية اختبار النظام
            </h3>
            <ol className="list-decimal list-inside space-y-2 text-blue-700">
              <li>افتح هذه الصفحة في عدة نوافذ أو متصفحات مختلفة</li>
              <li>أضف منتجات مختلفة في كل نافذة</li>
              <li>تأكد أن كل نافذة لها معرف جلسة مختلف</li>
              <li>تأكد أن المنتجات لا تظهر في النوافذ الأخرى</li>
              <li>جرب إنشاء جلسة جديدة ولاحظ تفريغ العربة</li>
            </ol>
          </div>
        </div>
      </div>

      <Footer />
    </div>
  );
}
