'use client';

import { useParams } from 'next/navigation';
import { Locale } from '../../lib/i18n';
import Navbar from '../../components/Navbar';
import Footer from '../../components/Footer';
import HeroSection from '../../components/HeroSection';
import FeaturedProducts from '../../components/FeaturedProducts';
import CategoriesSection from '../../components/CategoriesSection';
import WhatsAppButton from '../../components/WhatsAppButton';
import ServicesSection from '../../components/ServicesSection';
import PartnersSection from '../../components/PartnersSection';
import ScrollReveal from '../../components/ScrollReveal';

export default function HomePage() {
  const params = useParams();
  const locale = (params?.locale || 'ar') as Locale;

  return (
    <>
      <Navbar locale={locale} />
      <main className="smooth-scroll">
        <HeroSection locale={locale} />

        <div className="section-divider"></div>

        <ScrollReveal delay={200}>
          <ServicesSection locale={locale} />
        </ScrollReveal>

        <div className="section-divider"></div>

        <ScrollReveal delay={300} direction="left">
          <CategoriesSection locale={locale} />
        </ScrollReveal>

        <div className="section-divider"></div>

        <ScrollReveal delay={400} direction="right">
          <FeaturedProducts locale={locale} />
        </ScrollReveal>

        <div className="section-divider"></div>

        <ScrollReveal delay={500}>
          <PartnersSection locale={locale} />
        </ScrollReveal>
      </main>
      <Footer locale={locale} />
      <WhatsAppButton locale={locale} />
    </>
  );
}
