'use client';

import React, { useState, useEffect } from 'react';
import { Locale } from '../lib/i18n';

interface ServicesSectionProps {
  locale: Locale;
}

const ServicesSection: React.FC<ServicesSectionProps> = ({ locale }) => {
  const [whatsappNumber, setWhatsappNumber] = useState('+966501234567');

  useEffect(() => {
    // قراءة رقم الواتساب من الإعدادات
    const savedSettings = localStorage.getItem('siteSettings');
    if (savedSettings) {
      try {
        const settings = JSON.parse(savedSettings);
        // استخدام فقط إعدادات التواصل الجديدة، تجاهل الرقم القديم
        if (settings.communicationSettings?.whatsapp?.businessNumber) {
          setWhatsappNumber(settings.communicationSettings.whatsapp.businessNumber);
        }
      } catch (error) {
        console.log('Using default WhatsApp number');
      }
    }
  }, []);
  const services = [
    {
      icon: 'ri-tools-line',
      title: locale === 'ar' ? 'التركيب والصيانة' : 'Installation & Maintenance',
      description: locale === 'ar' 
        ? 'نوفر خدمات التركيب المهني والصيانة الدورية لجميع المعدات'
        : 'We provide professional installation and regular maintenance for all equipment',
      features: [
        locale === 'ar' ? 'تركيب احترافي' : 'Professional installation',
        locale === 'ar' ? 'صيانة دورية' : 'Regular maintenance',
        locale === 'ar' ? 'دعم فني 24/7' : '24/7 technical support'
      ]
    },
    {
      icon: 'ri-truck-line',
      title: locale === 'ar' ? 'التوصيل السريع' : 'Fast Delivery',
      description: locale === 'ar' 
        ? 'خدمة توصيل سريعة وآمنة لجميع أنحاء المملكة والخليج'
        : 'Fast and secure delivery service throughout the Kingdom and Gulf',
      features: [
        locale === 'ar' ? 'توصيل مجاني' : 'Free delivery',
        locale === 'ar' ? 'تغليف آمن' : 'Safe packaging',
        locale === 'ar' ? 'تتبع الشحنة' : 'Shipment tracking'
      ]
    },
    {
      icon: 'ri-customer-service-2-line',
      title: locale === 'ar' ? 'الاستشارة المجانية' : 'Free Consultation',
      description: locale === 'ar' 
        ? 'فريق من الخبراء لتقديم الاستشارة المجانية وتصميم الحلول المناسبة'
        : 'Team of experts to provide free consultation and design suitable solutions',
      features: [
        locale === 'ar' ? 'استشارة مجانية' : 'Free consultation',
        locale === 'ar' ? 'تصميم مخصص' : 'Custom design',
        locale === 'ar' ? 'خبرة 15+ سنة' : '15+ years experience'
      ]
    },
    {
      icon: 'ri-shield-check-line',
      title: locale === 'ar' ? 'الضمان الشامل' : 'Comprehensive Warranty',
      description: locale === 'ar' 
        ? 'ضمان شامل على جميع المنتجات مع خدمة ما بعد البيع المتميزة'
        : 'Comprehensive warranty on all products with excellent after-sales service',
      features: [
        locale === 'ar' ? 'ضمان 5 سنوات' : '5-year warranty',
        locale === 'ar' ? 'قطع غيار أصلية' : 'Original spare parts',
        locale === 'ar' ? 'خدمة ما بعد البيع' : 'After-sales service'
      ]
    }
  ];

  const content = {
    ar: {
      title: 'خدماتنا المتميزة',
      subtitle: 'نقدم مجموعة شاملة من الخدمات لضمان رضاكم التام',
      cta: 'اطلب خدمة',
      learnMore: 'اعرف المزيد'
    },
    en: {
      title: 'Our Distinguished Services',
      subtitle: 'We provide a comprehensive range of services to ensure your complete satisfaction',
      cta: 'Request Service',
      learnMore: 'Learn More'
    }
  };

  const currentContent = content[locale];

  return (
    <section className="py-24 bg-gradient-to-br from-gray-50 via-white to-blue-50 relative overflow-hidden">
      {/* Background decorative elements */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute top-20 left-10 w-32 h-32 bg-primary rounded-full animate-float"></div>
        <div className="absolute bottom-20 right-10 w-24 h-24 bg-secondary rounded-full animate-float" style={{animationDelay: '1s'}}></div>
        <div className="absolute top-1/2 left-1/4 w-16 h-16 bg-accent rounded-full animate-float" style={{animationDelay: '2s'}}></div>
      </div>

      <div className="container mx-auto px-4 relative z-10">
        {/* Header */}
        <div className="text-center mb-20">
          <div className="inline-block mb-6">
            <span className="bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent text-sm font-bold uppercase tracking-wider">
              {locale === 'ar' ? 'خدماتنا المتميزة' : 'Our Distinguished Services'}
            </span>
          </div>
          <h2 className="text-5xl md:text-6xl font-bold bg-gradient-to-r from-dark-800 via-primary-600 to-dark-800 bg-clip-text text-transparent mb-6 leading-tight">
            {currentContent.title}
          </h2>
          <div className="w-24 h-1 bg-gradient-to-r from-primary to-secondary mx-auto mb-6 rounded-full"></div>
          <p className="text-xl md:text-2xl text-gray-600 max-w-4xl mx-auto leading-relaxed">
            {currentContent.subtitle}
          </p>
        </div>

        {/* Services Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {services.map((service, index) => (
            <div
              key={index}
              className="group card-modern bg-white rounded-3xl p-8 hover:shadow-glow-lg transition-all duration-500 transform hover:-translate-y-4 border border-gray-100 hover:border-primary/30 relative overflow-hidden"
            >
              {/* Background gradient overlay */}
              <div className="absolute inset-0 bg-gradient-to-br from-primary/5 via-transparent to-secondary/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

              {/* Icon */}
              <div className="relative z-10 w-20 h-20 bg-gradient-to-br from-primary/10 to-secondary/10 rounded-3xl flex items-center justify-center mb-8 group-hover:scale-110 group-hover:shadow-glow transition-all duration-500 mx-auto">
                <div className="w-16 h-16 bg-gradient-to-br from-primary to-secondary rounded-2xl flex items-center justify-center group-hover:animate-glow">
                  <i className={`${service.icon} text-3xl text-white`}></i>
                </div>
              </div>

              {/* Content */}
              <div className="relative z-10 text-center">
                <h3 className="text-xl font-bold text-gray-800 mb-4 group-hover:text-primary transition-colors duration-300">
                  {service.title}
                </h3>

                <p className="text-gray-600 mb-6 leading-relaxed">
                  {service.description}
                </p>

                {/* Features */}
                <ul className="space-y-3 mb-8">
                  {service.features.map((feature, featureIndex) => (
                    <li key={featureIndex} className="flex items-center justify-center text-sm text-gray-600 group-hover:text-gray-700 transition-colors duration-300">
                      <div className="w-5 h-5 bg-gradient-to-r from-green-400 to-green-600 rounded-full flex items-center justify-center mr-3 flex-shrink-0">
                        <i className="ri-check-line text-white text-xs"></i>
                      </div>
                      <span>{feature}</span>
                    </li>
                  ))}
                </ul>

                {/* CTA Button */}
                <a
                  href={`/${locale}/contact`}
                  className="inline-flex items-center gap-2 bg-gradient-to-r from-primary to-secondary text-white px-6 py-3 rounded-xl font-semibold hover:shadow-glow transition-all duration-300 group-hover:gap-3 transform hover:scale-105"
                >
                  <span>{currentContent.learnMore}</span>
                  <i className="ri-arrow-right-line transition-transform duration-300 group-hover:translate-x-1"></i>
                </a>
              </div>

              {/* Decorative elements */}
              <div className="absolute top-4 right-4 w-8 h-8 bg-gradient-to-br from-primary/20 to-secondary/20 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-500 animate-pulse"></div>
              <div className="absolute bottom-4 left-4 w-6 h-6 bg-gradient-to-br from-accent/20 to-primary/20 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-500 animate-pulse" style={{animationDelay: '0.5s'}}></div>
            </div>
          ))}
        </div>

        {/* Bottom CTA */}
        <div className="text-center mt-20">
          <div className="relative bg-gradient-to-br from-dark-800 via-primary-700 to-secondary-700 rounded-3xl p-12 text-white overflow-hidden shadow-glow-lg">
            {/* Background decorative elements */}
            <div className="absolute inset-0 opacity-10">
              <div className="absolute top-4 right-4 w-20 h-20 bg-white rounded-full animate-float"></div>
              <div className="absolute bottom-4 left-4 w-16 h-16 bg-accent rounded-full animate-float" style={{animationDelay: '1s'}}></div>
              <div className="absolute top-1/2 left-1/2 w-12 h-12 bg-secondary rounded-full animate-float" style={{animationDelay: '0.5s'}}></div>
            </div>

            <div className="relative z-10">
              <div className="w-16 h-16 bg-gradient-to-br from-white/20 to-white/10 rounded-2xl flex items-center justify-center mx-auto mb-6 animate-glow">
                <i className="ri-customer-service-2-line text-3xl text-white"></i>
              </div>

              <h3 className="text-3xl md:text-4xl font-bold mb-6">
                {locale === 'ar'
                  ? 'هل تحتاج إلى خدمة مخصصة؟'
                  : 'Need a custom service?'
                }
              </h3>
              <p className="text-white/90 mb-8 max-w-3xl mx-auto text-lg leading-relaxed">
                {locale === 'ar'
                  ? 'تواصل معنا للحصول على حلول مخصصة تناسب احتياجاتك الخاصة مع فريق من الخبراء المتخصصين'
                  : 'Contact us to get custom solutions that suit your specific needs with a team of specialized experts'
                }
              </p>
              <div className="flex flex-col sm:flex-row gap-6 justify-center">
                <a
                  href={`/${locale}/contact`}
                  className="btn-modern bg-white text-primary px-10 py-4 rounded-2xl font-bold text-lg hover:bg-gray-100 hover:shadow-glow transition-all duration-300 inline-flex items-center justify-center gap-3 transform hover:scale-105"
                >
                  <i className="ri-phone-line text-xl"></i>
                  <span>{currentContent.cta}</span>
                </a>
                <a
                  href={`https://wa.me/${whatsappNumber.replace(/[^0-9]/g, '')}?text=${encodeURIComponent(locale === 'ar' ? 'مرحباً، أريد الاستفسار عن خدماتكم' : 'Hello, I would like to inquire about your services')}`}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="btn-modern glass-effect border-2 border-white/30 text-white hover:bg-white/10 px-10 py-4 rounded-2xl font-bold text-lg transition-all duration-300 inline-flex items-center justify-center gap-3 transform hover:scale-105"
                >
                  <i className="ri-whatsapp-line text-xl animate-bounce-slow"></i>
                  <span>{locale === 'ar' ? 'واتساب' : 'WhatsApp'}</span>
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default ServicesSection;
