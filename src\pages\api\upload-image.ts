import { NextApiRequest, NextApiResponse } from 'next';
import formidable from 'formidable';
import fs from 'fs';
import path from 'path';
import jwt from 'jsonwebtoken';

const JWT_SECRET = process.env.JWT_SECRET || 'your-super-secret-jwt-key-change-this-in-production';

// تعطيل body parser الافتراضي لـ Next.js
export const config = {
  api: {
    bodyParser: false,
  },
};

// التحقق من صحة JWT token
function verifyToken(token: string) {
  try {
    return jwt.verify(token, JWT_SECRET) as any;
  } catch (error) {
    return null;
  }
}

// استخراج token من الطلب
function extractToken(req: NextApiRequest): string | null {
  const authHeader = req.headers.authorization;
  if (authHeader && authHeader.startsWith('Bearer ')) {
    return authHeader.substring(7);
  }
  
  const tokenFromCookie = req.cookies.authToken;
  if (tokenFromCookie) {
    return tokenFromCookie;
  }
  
  return null;
}

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({
      success: false,
      message: 'Method not allowed',
      messageAr: 'الطريقة غير مسموحة'
    });
  }

  // التحقق من المصادقة
  const token = extractToken(req);
  if (!token) {
    return res.status(401).json({
      success: false,
      message: 'Authentication required',
      messageAr: 'المصادقة مطلوبة'
    });
  }

  const decoded = verifyToken(token);
  if (!decoded) {
    return res.status(401).json({
      success: false,
      message: 'Invalid token',
      messageAr: 'رمز المصادقة غير صحيح'
    });
  }

  try {
    // إنشاء مجلد uploads إذا لم يكن موجوداً
    const uploadsDir = path.join(process.cwd(), 'public', 'uploads');
    if (!fs.existsSync(uploadsDir)) {
      fs.mkdirSync(uploadsDir, { recursive: true });
      console.log('📁 تم إنشاء مجلد uploads');
    }

    // إعداد formidable لمعالجة رفع الملفات
    const form = formidable({
      uploadDir: uploadsDir,
      keepExtensions: true,
      maxFileSize: 10 * 1024 * 1024, // 10MB
      filter: function ({ name, originalFilename, mimetype }) {
        // السماح فقط بالصور
        return mimetype && mimetype.includes('image');
      },
    });

    // معالجة رفع الملف
    const [fields, files] = await form.parse(req);
    
    const uploadedFile = Array.isArray(files.image) ? files.image[0] : files.image;
    
    if (!uploadedFile) {
      return res.status(400).json({
        success: false,
        message: 'No image file uploaded',
        messageAr: 'لم يتم رفع أي ملف صورة'
      });
    }

    // إنشاء اسم ملف فريد
    const timestamp = Date.now();
    const randomString = Math.random().toString(36).substring(2, 15);
    const fileExtension = path.extname(uploadedFile.originalFilename || '');
    const newFileName = `hero-${timestamp}-${randomString}${fileExtension}`;
    const newFilePath = path.join(uploadsDir, newFileName);

    // نقل الملف إلى الاسم الجديد
    fs.renameSync(uploadedFile.filepath, newFilePath);

    // إنشاء URL للصورة
    const imageUrl = `/uploads/${newFileName}`;

    console.log('✅ تم رفع صورة الهيرو بنجاح:', newFileName);

    res.status(200).json({
      success: true,
      message: 'Image uploaded successfully',
      messageAr: 'تم رفع الصورة بنجاح',
      imageUrl,
      fileName: newFileName
    });

  } catch (error) {
    console.error('❌ خطأ في رفع الصورة:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to upload image',
      messageAr: 'فشل في رفع الصورة',
      error: process.env.NODE_ENV === 'development' ? (error as Error).message : undefined
    });
  }
}
