import CryptoJS from 'crypto-js';
import fs from 'fs';
import path from 'path';
import { AdminUser, hashPassword } from './auth';

// مفتاح التشفير - يجب أن يكون في متغيرات البيئة
const ENCRYPTION_KEY = process.env.ENCRYPTION_KEY || 'your-encryption-key-change-this-in-production';

// مسار ملف البيانات المشفر
const SECURE_DATA_PATH = path.join(process.cwd(), 'data', 'secure-admin.enc');

// نوع بيانات المستخدم المشفرة
interface SecureAdminData {
  id: string;
  username: string;
  email: string;
  passwordHash: string;
  role: string;
  lastLogin: Date;
  createdAt: Date;
  updatedAt: Date;
}

// تشفير البيانات
function encryptData(data: any): string {
  try {
    const jsonString = JSON.stringify(data);
    const encrypted = CryptoJS.AES.encrypt(jsonString, ENCRYPTION_KEY).toString();
    return encrypted;
  } catch (error) {
    console.error('Error encrypting data:', error);
    throw new Error('Failed to encrypt data');
  }
}

// فك تشفير البيانات
function decryptData(encryptedData: string): any {
  try {
    const decrypted = CryptoJS.AES.decrypt(encryptedData, ENCRYPTION_KEY);
    const jsonString = decrypted.toString(CryptoJS.enc.Utf8);
    return JSON.parse(jsonString);
  } catch (error) {
    console.error('Error decrypting data:', error);
    throw new Error('Failed to decrypt data');
  }
}

// إنشاء مجلد البيانات إذا لم يكن موجوداً
function ensureDataDirectory(): void {
  const dataDir = path.dirname(SECURE_DATA_PATH);
  if (!fs.existsSync(dataDir)) {
    fs.mkdirSync(dataDir, { recursive: true });
  }
}

// قراءة البيانات المشفرة
function readSecureData(): SecureAdminData | null {
  try {
    ensureDataDirectory();
    
    if (!fs.existsSync(SECURE_DATA_PATH)) {
      return null;
    }
    
    const encryptedData = fs.readFileSync(SECURE_DATA_PATH, 'utf8');
    const decryptedData = decryptData(encryptedData);
    return decryptedData;
  } catch (error) {
    console.error('Error reading secure data:', error);
    return null;
  }
}

// كتابة البيانات المشفرة
function writeSecureData(data: SecureAdminData): void {
  try {
    ensureDataDirectory();
    const encryptedData = encryptData(data);
    fs.writeFileSync(SECURE_DATA_PATH, encryptedData, 'utf8');
  } catch (error) {
    console.error('Error writing secure data:', error);
    throw new Error('Failed to save secure data');
  }
}

// إنشاء مستخدم إداري افتراضي آمن
export async function createDefaultAdminUser(): Promise<SecureAdminData> {
  const defaultPassword = process.env.DEFAULT_ADMIN_PASSWORD || 'TempAdmin@2024!';
  const passwordHash = await hashPassword(defaultPassword);
  
  const adminUser: SecureAdminData = {
    id: '1',
    username: 'admin',
    email: process.env.ADMIN_EMAIL || '<EMAIL>',
    passwordHash,
    role: 'admin',
    lastLogin: new Date(),
    createdAt: new Date(),
    updatedAt: new Date()
  };
  
  writeSecureData(adminUser);
  
  // طباعة كلمة المرور الافتراضية في وحدة التحكم (للمرة الأولى فقط)
  console.log('🔐 Default admin user created:');
  console.log('Username: admin');
  console.log('Password:', defaultPassword);
  console.log('⚠️  Please change this password immediately after first login!');
  
  return adminUser;
}

// الحصول على المستخدم الإداري
export async function getAdminUser(): Promise<SecureAdminData> {
  let adminData = readSecureData();
  
  if (!adminData) {
    // إنشاء مستخدم افتراضي إذا لم يكن موجوداً
    adminData = await createDefaultAdminUser();
  }
  
  return adminData;
}

// تحديث بيانات المستخدم الإداري
export async function updateAdminUser(updates: Partial<SecureAdminData>): Promise<SecureAdminData> {
  const currentData = await getAdminUser();
  
  const updatedData: SecureAdminData = {
    ...currentData,
    ...updates,
    updatedAt: new Date()
  };
  
  writeSecureData(updatedData);
  return updatedData;
}

// تغيير كلمة المرور
export async function changeAdminPassword(newPassword: string): Promise<void> {
  const passwordHash = await hashPassword(newPassword);
  await updateAdminUser({ passwordHash });
}

// التحقق من بيانات تسجيل الدخول (من قاعدة البيانات العادية)
export async function validateAdminLogin(username: string, password: string): Promise<AdminUser | null> {
  try {
    // قراءة من قاعدة البيانات العادية
    const fs = require('fs');
    const path = require('path');
    const DB_PATH = path.join(process.cwd(), 'src/data/database.json');

    if (!fs.existsSync(DB_PATH)) {
      console.error('Database file not found');
      return null;
    }

    const database = JSON.parse(fs.readFileSync(DB_PATH, 'utf8'));
    const adminUser = database.adminUser;

    if (!adminUser || adminUser.username !== username) {
      return null;
    }

    const bcrypt = require('bcryptjs');
    const isValidPassword = await bcrypt.compare(password, adminUser.password);

    if (!isValidPassword) {
      return null;
    }

    // تحديث وقت آخر تسجيل دخول
    adminUser.lastLogin = new Date().toISOString();
    fs.writeFileSync(DB_PATH, JSON.stringify(database, null, 2));

    // إرجاع بيانات المستخدم بدون كلمة المرور
    return {
      id: adminUser.id,
      username: adminUser.username,
      email: adminUser.email,
      role: adminUser.role,
      lastLogin: adminUser.lastLogin
    };
  } catch (error) {
    console.error('Error validating admin login:', error);
    return null;
  }
}

// حذف جميع البيانات (للطوارئ فقط)
export function resetAdminData(): void {
  try {
    if (fs.existsSync(SECURE_DATA_PATH)) {
      fs.unlinkSync(SECURE_DATA_PATH);
    }
  } catch (error) {
    console.error('Error resetting admin data:', error);
  }
}
