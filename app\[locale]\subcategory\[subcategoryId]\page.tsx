'use client';

import { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { Locale } from '../../../../lib/i18n';
import { getTranslation } from '../../../../lib/translations';
import Navbar from '../../../../components/Navbar';
import Footer from '../../../../components/Footer';
import ProductCard from '../../../../components/ProductCard';
import WhatsAppButton from '../../../../components/WhatsAppButton';
import { ProductWithDetails, Category, Subcategory } from '../../../../src/types/mysql-database';

export default function SubcategoryPage() {
  const params = useParams();
  const router = useRouter();
  const locale = (params?.locale || 'ar') as Locale;
  const subcategoryId = params?.subcategoryId as string;
  
  const [products, setProducts] = useState<ProductWithDetails[]>([]);
  const [subcategory, setSubcategory] = useState<Subcategory | null>(null);
  const [category, setCategory] = useState<Category | null>(null);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [sortBy, setSortBy] = useState('name');
  const [availabilityFilter, setAvailabilityFilter] = useState('all');

  const t = (key: string) => getTranslation(locale, key as any);

  useEffect(() => {
    if (subcategoryId) {
      fetchSubcategoryData();
    }
  }, [subcategoryId]);

  const fetchSubcategoryData = async () => {
    try {
      setLoading(true);
      
      // جلب بيانات الفئة الفرعية
      const subcategoriesResponse = await fetch('/api/subcategories');
      if (subcategoriesResponse.ok) {
        const subcategoriesData = await subcategoriesResponse.json();
        const currentSubcategory = subcategoriesData.find((sub: Subcategory) => sub.id === subcategoryId);
        
        if (!currentSubcategory) {
          router.push(`/${locale}/products`);
          return;
        }
        
        setSubcategory(currentSubcategory);

        // جلب بيانات الفئة الرئيسية
        const categoriesResponse = await fetch('/api/categories');
        if (categoriesResponse.ok) {
          const categoriesData = await categoriesResponse.json();
          const parentCategory = categoriesData.find((cat: Category) => cat.id === currentSubcategory.category_id);
          setCategory(parentCategory || null);
        }
      }
      
      // جلب المنتجات الخاصة بالفئة الفرعية
      const productsResponse = await fetch(`/api/products?subcategoryId=${subcategoryId}`);
      if (productsResponse.ok) {
        const productsData = await productsResponse.json();
        setProducts(productsData || []);
      }
      
    } catch (error) {
      console.error('Error fetching subcategory data:', error);
    } finally {
      setLoading(false);
    }
  };

  const filteredProducts = products.filter(product => {
    // فلتر البحث
    const searchField = locale === 'ar' ? product.title_ar : product.title;
    const matchesSearch = searchField.toLowerCase().includes(searchTerm.toLowerCase());
    
    // فلتر التوفر
    const matchesAvailability = availabilityFilter === 'all' || 
      (availabilityFilter === 'available' && product.is_available) ||
      (availabilityFilter === 'unavailable' && !product.is_available);
    
    return matchesSearch && matchesAvailability && product.is_active;
  });

  // ترتيب المنتجات
  const sortedProducts = [...filteredProducts].sort((a, b) => {
    switch (sortBy) {
      case 'name':
        const nameA = locale === 'ar' ? a.title_ar : a.title;
        const nameB = locale === 'ar' ? b.title_ar : b.title;
        return nameA.localeCompare(nameB);
      case 'price-low':
        return a.price - b.price;
      case 'price-high':
        return b.price - a.price;
      case 'newest':
        return new Date(b.created_at || '').getTime() - new Date(a.created_at || '').getTime();
      default:
        return 0;
    }
  });

  if (loading) {
    return (
      <>
        <Navbar locale={locale} />
        <main className="min-h-screen bg-gray-50 py-8">
          <div className="container mx-auto px-4">
            <div className="text-center py-16">
              <div className="animate-spin rounded-full h-16 w-16 border-4 border-primary/20 border-t-primary mx-auto mb-6"></div>
              <p className="text-gray-700 text-lg font-medium">
                {locale === 'ar' ? 'جاري تحميل منتجات الفئة الفرعية...' : 'Loading subcategory products...'}
              </p>
            </div>
          </div>
        </main>
        <Footer locale={locale} />
        <WhatsAppButton locale={locale} />
      </>
    );
  }

  if (!subcategory) {
    return (
      <>
        <Navbar locale={locale} />
        <main className="min-h-screen bg-gray-50 py-8">
          <div className="container mx-auto px-4">
            <div className="text-center py-16">
              <i className="ri-error-warning-line text-6xl text-red-400 mb-4"></i>
              <h3 className="text-xl font-semibold text-gray-800 mb-2">
                {locale === 'ar' ? 'الفئة الفرعية غير موجودة' : 'Subcategory not found'}
              </h3>
              <p className="text-gray-600 mb-6">
                {locale === 'ar' 
                  ? 'الفئة الفرعية التي تبحث عنها غير موجودة أو تم حذفها'
                  : 'The subcategory you are looking for does not exist or has been deleted'
                }
              </p>
              <button
                onClick={() => router.push(`/${locale}/products`)}
                className="bg-primary hover:bg-primary/90 text-white px-6 py-3 rounded-lg font-semibold transition-all duration-300"
              >
                {locale === 'ar' ? 'العودة للمنتجات' : 'Back to Products'}
              </button>
            </div>
          </div>
        </main>
        <Footer locale={locale} />
        <WhatsAppButton locale={locale} />
      </>
    );
  }

  return (
    <>
      <Navbar locale={locale} />
      <main className="min-h-screen bg-gray-50">
        {/* Page Header */}
        <section className="bg-gradient-to-r from-blue-900 via-blue-800 to-blue-700 py-16">
          <div className="container mx-auto px-4">
            {/* Breadcrumb */}
            <nav className="flex items-center space-x-2 rtl:space-x-reverse text-sm text-white/80 mb-6">
              <a href={`/${locale}`} className="hover:text-white transition-colors">
                {t('home')}
              </a>
              <span>/</span>
              {category && (
                <>
                  <a 
                    href={`/${locale}/category/${category.id}`} 
                    className="hover:text-white transition-colors"
                  >
                    {locale === 'ar' ? category.name_ar : category.name}
                  </a>
                  <span>/</span>
                </>
              )}
              <span className="text-white font-medium">
                {locale === 'ar' ? subcategory.name_ar : subcategory.name}
              </span>
            </nav>
            
            <div className="text-center text-white">
              <h1 className="text-4xl md:text-5xl font-bold mb-4">
                {locale === 'ar' ? subcategory.name_ar : subcategory.name}
              </h1>
              <p className="text-xl text-white/90 max-w-3xl mx-auto mb-6">
                {locale === 'ar' ? subcategory.description_ar : subcategory.description}
              </p>
              <div className="flex items-center justify-center space-x-4 rtl:space-x-reverse text-white/80">
                <span className="flex items-center space-x-2 rtl:space-x-reverse">
                  <i className="ri-shopping-bag-line"></i>
                  <span>{sortedProducts.length} {locale === 'ar' ? 'منتج' : 'Products'}</span>
                </span>
              </div>
            </div>
          </div>
        </section>

        {/* Products Section */}
        <section className="py-12">
          <div className="container mx-auto px-4">
            <div className="flex flex-col lg:flex-row gap-8">
              {/* Sidebar Filters */}
              <div className="lg:w-1/4">
                {/* Search Filter */}
                <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-4">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    {locale === 'ar' ? 'البحث' : 'Search'}
                  </label>
                  <input
                    type="text"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-1 focus:ring-blue-500 focus:border-blue-500 text-sm"
                    placeholder={locale === 'ar' ? 'ابحث في المنتجات...' : 'Search products...'}
                  />
                </div>

                {/* Availability Filter */}
                <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-4">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    {locale === 'ar' ? 'التوفر' : 'Availability'}
                  </label>
                  <select
                    value={availabilityFilter}
                    onChange={(e) => setAvailabilityFilter(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-1 focus:ring-blue-500 focus:border-blue-500 text-sm"
                  >
                    <option value="all">
                      {locale === 'ar' ? 'جميع المنتجات' : 'All Products'}
                    </option>
                    <option value="available">
                      {locale === 'ar' ? 'متاح فقط' : 'Available Only'}
                    </option>
                    <option value="unavailable">
                      {locale === 'ar' ? 'غير متاح' : 'Unavailable'}
                    </option>
                  </select>
                </div>

                {/* Sort Filter */}
                <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-4">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    {locale === 'ar' ? 'ترتيب حسب' : 'Sort by'}
                  </label>
                  <select
                    value={sortBy}
                    onChange={(e) => setSortBy(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-1 focus:ring-blue-500 focus:border-blue-500 text-sm"
                  >
                    <option value="name">
                      {locale === 'ar' ? 'الاسم' : 'Name'}
                    </option>
                    <option value="price-low">
                      {locale === 'ar' ? 'السعر: من الأقل للأعلى' : 'Price: Low to High'}
                    </option>
                    <option value="price-high">
                      {locale === 'ar' ? 'السعر: من الأعلى للأقل' : 'Price: High to Low'}
                    </option>
                    <option value="newest">
                      {locale === 'ar' ? 'الأحدث' : 'Newest'}
                    </option>
                  </select>
                </div>

                {/* Clear Filters */}
                <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
                  <button
                    onClick={() => {
                      setSearchTerm('');
                      setAvailabilityFilter('all');
                      setSortBy('name');
                    }}
                    className="w-full bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors duration-200"
                  >
                    {locale === 'ar' ? 'مسح الفلاتر' : 'Clear Filters'}
                  </button>
                </div>
              </div>

              {/* Main Content */}
              <div className="lg:w-3/4">
                {/* Results Summary */}
                <div className="bg-white rounded-xl p-4 shadow-sm border border-gray-100 mb-6">
                  <div className="flex items-center justify-between">
                    <span className="text-gray-600">
                      {locale === 'ar' ? 'عرض' : 'Showing'}
                      <span className="font-semibold text-blue-600 mx-1">{sortedProducts.length}</span>
                      {locale === 'ar' ? 'من أصل' : 'of'}
                      <span className="font-semibold text-blue-600 mx-1">{products.length}</span>
                      {locale === 'ar' ? 'منتج' : 'products'}
                    </span>
                  </div>
                </div>

                {/* Products Grid */}
                {sortedProducts.length > 0 ? (
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    {sortedProducts.map((product) => (
                      <ProductCard
                        key={product.id}
                        id={product.id}
                        image={product.images?.[0]?.image_url || '/api/placeholder?width=400&height=300&text=لا توجد صورة'}
                        title={locale === 'ar' ? product.title_ar : product.title}
                        description={locale === 'ar' ? product.description_ar : product.description}
                        price={product.price}
                        available={product.is_available}
                        locale={locale}
                      />
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-16 bg-white rounded-2xl shadow-lg">
                    <i className="ri-search-line text-6xl text-gray-400 mb-4"></i>
                    <h3 className="text-xl font-semibold text-gray-800 mb-2">
                      {locale === 'ar' ? 'لا توجد منتجات' : 'No products found'}
                    </h3>
                    <p className="text-gray-600 mb-6">
                      {locale === 'ar'
                        ? 'لا توجد منتجات في هذه الفئة الفرعية حالياً'
                        : 'No products are currently available in this subcategory'
                      }
                    </p>
                  </div>
                )}

                {/* Back Navigation */}
                <div className="mt-12 flex flex-col sm:flex-row gap-4 justify-center">
                  {category && (
                    <button
                      onClick={() => router.push(`/${locale}/category/${category.id}`)}
                      className="bg-gray-100 hover:bg-gray-200 text-gray-700 px-6 py-3 rounded-lg font-semibold transition-all duration-300 flex items-center justify-center space-x-2 rtl:space-x-reverse"
                    >
                      <i className="ri-arrow-left-line"></i>
                      <span>{locale === 'ar' ? 'العودة للفئة الرئيسية' : 'Back to Category'}</span>
                    </button>
                  )}
                  <button
                    onClick={() => router.push(`/${locale}/products`)}
                    className="bg-primary hover:bg-primary/90 text-white px-6 py-3 rounded-lg font-semibold transition-all duration-300 flex items-center justify-center space-x-2 rtl:space-x-reverse"
                  >
                    <i className="ri-shopping-bag-line"></i>
                    <span>{locale === 'ar' ? 'جميع المنتجات' : 'All Products'}</span>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </section>
      </main>
      <Footer locale={locale} />
      <WhatsAppButton locale={locale} />
    </>
  );
}
